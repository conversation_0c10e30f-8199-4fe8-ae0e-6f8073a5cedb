#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于LLM辅助的婴幼儿体格生长监测数据集构建器
使用Qwen API生成更多样化和自然的训练数据
"""

import pandas as pd
import json
import random
import requests
import time
import logging
from typing import Dict, List, Tuple, Any, Optional
import os
from datetime import datetime
from config import Config

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.FILE_PATHS["log_file"], encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class LLMAssistedDatasetBuilder:
    """基于LLM辅助的数据集构建器"""

    def __init__(self):
        self.excel_file = Config.FILE_PATHS["excel_file"]
        self.data_tables = {}
        self.dataset = []
        self.logger = logging.getLogger(__name__)

        # 验证配置
        if not Config.validate_config():
            raise ValueError("配置验证失败")

        # API调用配置
        self.headers = Config.get_api_headers()
        self.base_url = Config.QWEN_BASE_URL
        self.request_interval = Config.API_CONFIG["request_interval"]
        self.max_retries = Config.API_CONFIG["max_retries"]
        self.timeout = Config.API_CONFIG["timeout"]
        
    def call_qwen_api(self, prompt: str, **kwargs) -> Optional[str]:
        """调用Qwen API，支持重试机制"""
        payload = Config.get_api_payload(prompt, **kwargs)

        for attempt in range(self.max_retries):
            try:
                self.logger.debug(f"API调用尝试 {attempt + 1}/{self.max_retries}")

                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload,
                    timeout=self.timeout
                )
                response.raise_for_status()

                result = response.json()
                if "output" in result and "text" in result["output"]:
                    return result["output"]["text"].strip()
                else:
                    self.logger.warning(f"API响应格式异常: {result}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"API调用失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避

            except Exception as e:
                self.logger.error(f"处理API响应时出错: {e}")
                break

        self.logger.error(f"API调用最终失败，已重试 {self.max_retries} 次")
        return None
    
    def load_excel_data(self):
        """加载Excel数据"""
        self.logger.info("正在加载Excel数据...")

        xl = pd.ExcelFile(self.excel_file)

        # 加载标准数据表
        for sheet_name in xl.sheet_names:
            if sheet_name.startswith('表 A.') and ('体重' in sheet_name or '身长' in sheet_name or '身高' in sheet_name or 'BMI' in sheet_name or '头围' in sheet_name):
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.data_tables[sheet_name] = df
                self.logger.info(f"已加载数据表: {sheet_name}")
    
    def generate_diverse_questions(self, base_scenario: Dict[str, Any], count: int = None) -> List[str]:
        """使用LLM生成多样化的问题表述"""

        if count is None:
            count = Config.DATASET_CONFIG["questions_per_scenario"]

        age = base_scenario['age']
        gender = base_scenario['gender']
        indicator = base_scenario['indicator']
        value = base_scenario['value']
        unit = base_scenario['unit']

        prompt = Config.PROMPT_TEMPLATES["question_generation"].format(
            count=count,
            age=age,
            gender=gender,
            indicator=indicator,
            value=value,
            unit=unit
        )

        response = self.call_qwen_api(prompt, max_tokens=500, temperature=0.8)
        if response:
            questions = [q.strip() for q in response.split('\n') if q.strip() and not q.strip().isdigit()]
            # 过滤掉过短或包含特殊字符的问题
            valid_questions = [q for q in questions if len(q) > 10 and not q.startswith(('示例', '格式', '要求'))]
            return valid_questions[:count]

        # 如果API调用失败，返回基础问题
        fallback_question = f"请评估一个{age}的{gender}童{indicator}{value}{unit}的生长水平"
        self.logger.warning(f"API调用失败，使用备用问题: {fallback_question}")
        return [fallback_question]

    def calculate_bmi(self, weight_kg: float, height_cm: float) -> float:
        """计算BMI"""
        if weight_kg <= 0 or height_cm <= 0:
            return None
        height_m = height_cm / 100
        return round(weight_kg / (height_m * height_m), 1)

    def generate_comprehensive_assessment(self, scenario: Dict[str, Any]) -> str:
        """生成包含BMI的综合评估"""

        age = scenario['age']
        gender = scenario['gender']
        weight = scenario['weight']
        height = scenario['height']
        bmi = self.calculate_bmi(weight, height)

        prompt = f"""
作为儿科医生，请对以下儿童生长发育情况给出综合评估：

患儿信息：
- 年龄：{age}
- 性别：{gender}童
- 体重：{weight}kg
- 身高：{height}cm
- BMI：{bmi}（已计算）

请提供：
1. 体重评估（结合年龄性别标准）
2. 身高评估（结合年龄性别标准）
3. BMI评估和营养状况分析
4. 综合生长发育结论
5. 具体的指导建议

要求：
- 语言专业但易懂
- 结构清晰，分点说明
- 长度200-300字
- 不要使用markdown格式
- 提供实用的建议
"""

        response = self.call_qwen_api(prompt, max_tokens=1000, temperature=0.6)
        if response:
            return response

        # 备用回答
        return f"根据7岁以下儿童生长发育标准，{age}的{gender}童体重{weight}kg，身高{height}cm，BMI为{bmi}。建议定期监测生长发育情况。"

    def generate_professional_assessment(self, scenario: Dict[str, Any]) -> str:
        """使用LLM生成专业的评估回答"""
        
        age = scenario['age']
        gender = scenario['gender']
        indicator = scenario['indicator']
        value = scenario['value']
        unit = scenario['unit']
        percentiles = scenario['percentiles']
        assessment_level = scenario['assessment_level']
        
        prompt = f"""
作为儿科医生，请对以下儿童生长发育情况给出专业评估：

患儿信息：
- 年龄：{age}
- 性别：{gender}童
- {indicator}：{value}{unit}

参考标准（百分位数）：
- P3: {percentiles.get('P3', 'N/A')}
- P10: {percentiles.get('P10', 'N/A')}
- P25: {percentiles.get('P25', 'N/A')}
- P50: {percentiles.get('P50', 'N/A')}
- P75: {percentiles.get('P75', 'N/A')}
- P90: {percentiles.get('P90', 'N/A')}
- P97: {percentiles.get('P97', 'N/A')}

评估水平：{assessment_level}

请提供：
1. 明确的评估结论
2. 参考标准说明
3. 具体的建议或注意事项
4. 必要时说明可能的原因

要求：
- 语言专业但易懂
- 结构清晰
- 长度适中（100-200字）
- 不要使用markdown格式
"""
        
        response = self.call_qwen_api(prompt, max_tokens=800, temperature=0.6)
        if response:
            return response
        
        # 如果API调用失败，返回基础评估
        return f"根据7岁以下儿童生长发育标准，{age}的{gender}童{indicator}{value}{unit}属于{assessment_level}水平。参考标准：P50为{percentiles.get('P50', 'N/A')}{unit}。"
    
    def generate_guidance_content(self, topic: str, context: str = "") -> str:
        """生成指导建议内容"""
        
        prompt = f"""
作为儿童保健专家，请就"{topic}"这个主题提供专业的指导建议。

{f"背景信息：{context}" if context else ""}

要求：
1. 内容专业、准确、实用
2. 结构清晰，要点明确
3. 适合家长理解和执行
4. 长度控制在150-300字
5. 不要使用markdown格式

请提供具体的、可操作的建议。
"""
        
        response = self.call_qwen_api(prompt, max_tokens=1000, temperature=0.7)
        return response if response else f"关于{topic}的专业指导建议。"
    
    def generate_case_analysis(self, case_info: Dict[str, Any]) -> str:
        """生成案例分析"""
        
        prompt = f"""
请对以下儿童生长发育案例进行专业分析：

案例信息：
{json.dumps(case_info, ensure_ascii=False, indent=2)}

请提供：
1. 案例分析
2. 生长发育评估
3. 可能的影响因素
4. 具体建议
5. 随访计划

要求：
- 分析全面、逻辑清晰
- 建议具体可行
- 语言专业但易懂
- 长度300-500字
- 不要使用markdown格式
"""
        
        response = self.call_qwen_api(prompt, max_tokens=1500, temperature=0.6)
        return response if response else "案例分析内容"
    
    def parse_age_column(self, age_str: str) -> Tuple[int, str]:
        """解析年龄列"""
        if pd.isna(age_str):
            return None, None

        age_str = str(age_str).strip()

        # 跳过注释行和无效数据
        if '注：' in age_str or '说明' in age_str or len(age_str) > 20:
            return None, None

        try:
            if '岁' in age_str and '月' in age_str:
                parts = age_str.split('岁')
                years = int(parts[0])
                months_part = parts[1].replace('月', '').strip()
                months = int(months_part) if months_part else 0
                total_months = years * 12 + months
                return total_months, '月'
            elif '月' in age_str:
                age_num = int(age_str.replace('月', ''))
                return age_num, '月'
            elif '岁' in age_str:
                age_num = float(age_str.replace('岁', ''))
                return int(age_num * 12), '月'
        except (ValueError, IndexError):
            # 如果解析失败，返回None
            return None, None

        return None, None
    
    def _clean_numeric_value(self, value):
        """清理数值"""
        if pd.isna(value):
            return None
        if isinstance(value, str):
            value = value.replace(' ', '').replace('.', '.')
            try:
                return float(value)
            except:
                return None
        return float(value)
    
    def get_growth_assessment(self, value: float, percentiles: Dict[str, float]) -> str:
        """评估生长水平"""
        if value >= percentiles.get('P97', float('inf')):
            return '上'
        elif value >= percentiles.get('P75', float('inf')):
            return '中上'
        elif value >= percentiles.get('P25', float('inf')):
            return '中'
        elif value >= percentiles.get('P10', float('inf')):
            return '中下'
        else:
            return '下'
    
    def generate_llm_assisted_samples(self):
        """生成LLM辅助的训练样本"""
        print("正在使用LLM生成训练样本...")

        sample_count = 0
        target_samples = Config.DATASET_CONFIG["target_samples"]

        print(f"目标生成 {target_samples} 个样本")

        # 首先生成包含头围的多指标综合评估样本（增加比例）
        multi_indicator_count = target_samples // 3  # 约133个样本
        sample_count += self.generate_multi_indicator_samples(multi_indicator_count)
        print(f"已生成多指标样本: {sample_count}")

        # 然后生成综合评估样本（体重+身高+BMI）
        comprehensive_count = target_samples // 4  # 约100个样本
        sample_count += self.generate_comprehensive_samples(comprehensive_count)
        print(f"已生成综合评估样本: {sample_count}")

        # 然后生成单项指标评估样本
        processed_tables = set()
        remaining_tables = [name for name in self.data_tables.keys()
                          if '年龄' in self.data_tables[name].columns and name not in processed_tables]

        for table_name, df in self.data_tables.items():
            if sample_count >= target_samples:
                break

            if '年龄' not in df.columns:
                continue

            print(f"处理数据表: {table_name}")

            # 确定指标类型
            if '体重' in table_name:
                indicator = '体重'
                unit = 'kg'
            elif '身长' in table_name or '身高' in table_name:
                indicator = '身高'
                unit = 'cm'
            elif 'BMI' in table_name:
                indicator = 'BMI'
                unit = ''
            elif '头围' in table_name:
                indicator = '头围'
                unit = 'cm'
                # 头围数据特别处理，生成更多样本
                remaining_samples = target_samples - sample_count
                head_samples = min(remaining_samples, 30)  # 限制头围样本数
                sample_count += self.generate_head_circumference_samples(df, table_name, head_samples)
                continue
            else:
                continue

            gender = '男' if '男童' in table_name else '女'
            
            # 选择有效数据行
            valid_rows = []
            for idx, row in df.iterrows():
                percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
                valid = True
                for col in percentile_cols:
                    if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                        valid = False
                        break
                if valid and pd.notna(row['年龄']):
                    valid_rows.append(row)
            
            if len(valid_rows) == 0:
                continue
            
            # 随机选择行生成样本
            sample_rows = random.sample(valid_rows, min(10, len(valid_rows)))
            
            for row in sample_rows:
                if sample_count >= target_samples:
                    break
                    
                age = row['年龄']
                
                # 清理百分位数数据
                percentiles = {}
                for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                    percentiles[col] = self._clean_numeric_value(row[col])
                
                # 生成不同的测试值
                test_values = [
                    percentiles['P25'],  # 中下
                    percentiles['P50'],  # 中等
                    percentiles['P75'],  # 中上
                ]
                
                for value in test_values:
                    if value is None or value <= 0:
                        continue
                    
                    assessment_level = self.get_growth_assessment(value, percentiles)
                    
                    # 构建场景信息
                    scenario = {
                        'age': age,
                        'gender': gender,
                        'indicator': indicator,
                        'value': value,
                        'unit': unit,
                        'percentiles': percentiles,
                        'assessment_level': assessment_level
                    }
                    
                    # 生成多样化问题
                    questions = self.generate_diverse_questions(scenario, count=2)
                    
                    for question in questions:
                        if sample_count >= target_samples:
                            break
                            
                        # 生成专业评估
                        assessment = self.generate_professional_assessment(scenario)
                        
                        sample = {
                            "instruction": question,
                            "input": "",
                            "output": assessment
                        }
                        
                        self.dataset.append(sample)
                        sample_count += 1
                        
                        print(f"已生成 {sample_count}/{target_samples} 个样本")
                        
                        # 控制API调用频率
                        time.sleep(self.request_interval)
                    
                    if sample_count >= target_samples:
                        break
                
                if sample_count >= target_samples:
                    break
            
            if sample_count >= target_samples:
                break
    
    def generate_knowledge_qa(self):
        """生成基础知识问答"""
        print("正在生成基础知识问答...")
        
        knowledge_topics = [
            "儿童生长发育监测的重要性",
            "如何正确测量儿童体重",
            "如何正确测量儿童身高",
            "儿童BMI的意义和计算方法",
            "儿童头围测量的重要性",
            "生长发育迟缓的识别和处理",
            "儿童营养不良的预防",
            "促进儿童健康生长的方法"
        ]
        
        for topic in knowledge_topics:
            # 生成问题
            question_prompt = f"请生成一个关于'{topic}'的专业问题，适合家长或医护人员询问。问题要简洁明确。"
            question = self.call_qwen_api(question_prompt, max_tokens=200, temperature=0.8)
            
            if question:
                # 生成回答
                answer = self.generate_guidance_content(topic)
                
                sample = {
                    "instruction": question,
                    "input": "",
                    "output": answer
                }
                
                self.dataset.append(sample)
                print(f"已生成知识问答: {topic}")
                
                time.sleep(self.request_interval)
    
    def save_dataset(self, output_file: str = "llm_assisted_growth_dataset.jsonl"):
        """保存数据集"""
        print(f"正在保存数据集到 {output_file}...")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in self.dataset:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"数据集已保存，共 {len(self.dataset)} 条记录")
        
        # 同时保存为JSON格式
        json_file = output_file.replace('.jsonl', '.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset, f, ensure_ascii=False, indent=2)
        
        print(f"同时保存JSON格式到 {json_file}")
    
    def build_llm_assisted_dataset(self):
        """构建LLM辅助的数据集"""
        print("开始构建LLM辅助的婴幼儿体格生长监测数据集...")
        
        # 加载数据
        self.load_excel_data()
        
        # 生成各类样本
        self.generate_llm_assisted_samples()
        self.generate_knowledge_qa()
        
        # 打乱数据集
        random.shuffle(self.dataset)
        
        # 保存数据集
        self.save_dataset()
        
        print("LLM辅助数据集构建完成！")

    def generate_knowledge_qa_extended(self, target_count: int):
        """生成扩展的基础知识问答"""
        print(f"正在生成扩展基础知识问答 ({target_count}个)...")

        extended_topics = [
            "儿童生长发育监测的重要性和意义",
            "如何正确测量儿童体重的方法和注意事项",
            "如何正确测量儿童身高身长的标准流程",
            "儿童BMI的计算方法和临床意义",
            "儿童头围测量的重要性和脑发育关系",
            "生长发育迟缓的早期识别和干预措施",
            "儿童营养不良的预防和改善方法",
            "促进儿童健康生长发育的综合策略",
            "儿童生长曲线图的正确解读方法",
            "不同年龄段儿童的生长发育特点",
            "儿童体格检查的标准流程和要点",
            "家长如何在家监测孩子的生长发育",
            "儿童生长发育异常的常见原因分析",
            "营养因素对儿童生长发育的影响",
            "睡眠对儿童生长发育的重要作用",
            "运动锻炼对儿童身高发育的促进作用",
            "遗传因素在儿童生长发育中的作用",
            "环境因素对儿童生长发育的影响",
            "儿童肥胖的预防和管理策略",
            "早产儿生长发育监测的特殊考虑"
        ]

        generated_count = 0
        for topic in extended_topics:
            if generated_count >= target_count:
                break

            # 生成问题
            question_prompt = f"请生成一个关于'{topic}'的专业问题，适合家长或医护人员询问。问题要简洁明确，体现实用性。"
            question = self.call_qwen_api(question_prompt, max_tokens=200, temperature=0.8)

            if question:
                # 生成回答
                answer = self.generate_guidance_content(topic)

                if answer:
                    sample = {
                        "instruction": question,
                        "input": "",
                        "output": answer
                    }

                    self.dataset.append(sample)
                    generated_count += 1
                    print(f"已生成知识问答 {generated_count}/{target_count}: {topic}")

                    time.sleep(self.request_interval)

        return generated_count

    def generate_multi_indicator_samples(self, target_count: int) -> int:
        """生成包含头围的多指标评估样本（体重+身高+头围）"""
        print("正在生成包含头围的多指标评估样本...")

        generated_count = 0

        # 获取各类数据表
        weight_tables = {name: df for name, df in self.data_tables.items() if '体重' in name and '年龄别' in name}
        height_tables = {name: df for name, df in self.data_tables.items() if ('身长' in name or '身高' in name) and '年龄别' in name}
        head_tables = {name: df for name, df in self.data_tables.items() if '头围' in name}

        for weight_table_name, weight_df in weight_tables.items():
            if generated_count >= target_count:
                break

            gender = '男' if '男童' in weight_table_name else '女'

            # 找到对应的身高表和头围表
            height_df = None
            head_df = None

            for height_table_name, df in height_tables.items():
                if gender in height_table_name:
                    height_df = df
                    break

            for head_table_name, df in head_tables.items():
                if gender in head_table_name:
                    head_df = df
                    break

            if height_df is None or head_df is None:
                continue

            # 找到0-3岁的年龄点（头围数据只到3岁）
            common_ages = []
            for _, weight_row in weight_df.iterrows():
                age = weight_row['年龄']
                age_num, _ = self.parse_age_column(age)

                # 只处理0-36月的数据（头围标准范围）
                if age_num is None or age_num > 36:
                    continue

                # 查找身高表和头围表中是否有相同年龄
                height_row = height_df[height_df['年龄'] == age]
                head_row = head_df[head_df['年龄'] == age]

                if (not height_row.empty and not head_row.empty and
                    pd.notna(weight_row['P50']) and pd.notna(height_row.iloc[0]['P50']) and pd.notna(head_row.iloc[0]['P50'])):
                    common_ages.append((age, weight_row, height_row.iloc[0], head_row.iloc[0]))

            # 随机选择几个年龄点
            selected_ages = random.sample(common_ages, min(3, len(common_ages)))

            for age, weight_row, height_row, head_row in selected_ages:
                if generated_count >= target_count:
                    break

                # 生成不同的指标组合
                weight_values = [weight_row['P25'], weight_row['P50'], weight_row['P75']]
                height_values = [height_row['P25'], height_row['P50'], height_row['P75']]
                head_values = [head_row['P25'], head_row['P50'], head_row['P75']]

                # 选择一组数值进行组合
                for i in range(min(2, len(weight_values))):  # 限制组合数量
                    if generated_count >= target_count:
                        break

                    weight = weight_values[i]
                    height = height_values[i]
                    head_circumference = head_values[i]

                    if (pd.isna(weight) or pd.isna(height) or pd.isna(head_circumference) or
                        weight <= 0 or height <= 0 or head_circumference <= 0):
                        continue

                    bmi = self.calculate_bmi(weight, height)
                    if bmi is None:
                        continue

                    # 生成包含头围的问题
                    questions = self.generate_multi_indicator_questions(age, gender, weight, height, head_circumference, bmi)

                    for question in questions[:1]:  # 每个组合生成1个问题
                        if generated_count >= target_count:
                            break

                        # 生成多指标评估
                        scenario = {
                            'age': age,
                            'gender': gender,
                            'weight': weight,
                            'height': height,
                            'head_circumference': head_circumference,
                            'bmi': bmi
                        }

                        assessment = self.generate_multi_indicator_assessment(scenario)

                        sample = {
                            "instruction": question,
                            "input": "",
                            "output": assessment
                        }

                        self.dataset.append(sample)
                        generated_count += 1

                        print(f"已生成多指标评估样本 {generated_count}/{target_count}")
                        time.sleep(self.request_interval)

        return generated_count

    def generate_multi_indicator_questions(self, age: str, gender: str, weight: float, height: float, head_circumference: float, bmi: float) -> List[str]:
        """生成包含头围的多指标问题"""

        prompt = f"""
请为儿童多指标生长发育评估生成2种不同的问题表述方式。

基础信息：
- 年龄：{age}
- 性别：{gender}童
- 体重：{weight}kg
- 身高：{height}cm
- 头围：{head_circumference}cm
- BMI：{bmi}（可提及也可不提及）

要求：
1. 问题要包含体重、身高、头围三个指标
2. 可以是家长咨询、医生评估、体检报告等场景
3. 表述自然、专业
4. 每行一个问题，不要编号
5. 强调头围对脑发育的重要性

示例格式：
请评估一个6月龄男婴体重7.2kg、身高65cm、头围42cm的综合发育情况
6个月男宝宝体重7.2公斤、身高65厘米、头围42厘米，各项指标是否正常？
"""

        response = self.call_qwen_api(prompt, max_tokens=400, temperature=0.8)
        if response:
            questions = [q.strip() for q in response.split('\n') if q.strip() and len(q.strip()) > 15]
            return questions[:2]

        # 备用问题
        return [f"请评估一个{age}的{gender}童体重{weight}kg、身高{height}cm、头围{head_circumference}cm的综合发育情况"]

    def generate_multi_indicator_assessment(self, scenario: Dict[str, Any]) -> str:
        """生成包含头围的多指标评估"""

        age = scenario['age']
        gender = scenario['gender']
        weight = scenario['weight']
        height = scenario['height']
        head_circumference = scenario['head_circumference']
        bmi = scenario['bmi']

        prompt = f"""
作为儿科医生，请对以下儿童多指标生长发育情况给出全面评估：

患儿信息：
- 年龄：{age}
- 性别：{gender}童
- 体重：{weight}kg
- 身高：{height}cm
- 头围：{head_circumference}cm
- BMI：{bmi}（已计算）

请提供：
1. 体重评估（结合年龄性别标准）
2. 身高评估（结合年龄性别标准）
3. 头围评估（强调脑发育意义）
4. BMI评估和营养状况分析
5. 综合生长发育结论
6. 具体的指导建议

要求：
- 语言专业但易懂
- 结构清晰，分点说明
- 特别关注头围与脑发育的关系
- 长度250-350字
- 不要使用markdown格式
- 提供实用的建议
"""

        response = self.call_qwen_api(prompt, max_tokens=1200, temperature=0.6)
        if response:
            return response

        # 备用回答
        return f"根据儿童生长发育标准，{age}的{gender}童体重{weight}kg、身高{height}cm、头围{head_circumference}cm、BMI{bmi}。各项指标需要结合标准曲线进行综合评估，建议定期监测生长发育情况。"

    def generate_comprehensive_samples(self, target_count: int) -> int:
        """生成综合评估样本（体重+身高+BMI）"""
        print("正在生成综合评估样本...")

        generated_count = 0

        # 获取体重和身高数据表
        weight_tables = {name: df for name, df in self.data_tables.items() if '体重' in name and '年龄别' in name}
        height_tables = {name: df for name, df in self.data_tables.items() if ('身长' in name or '身高' in name) and '年龄别' in name}

        for weight_table_name, weight_df in weight_tables.items():
            if generated_count >= target_count:
                break

            gender = '男' if '男童' in weight_table_name else '女'

            # 找到对应的身高表
            height_df = None
            for height_table_name, df in height_tables.items():
                if gender in height_table_name:
                    height_df = df
                    break

            if height_df is None:
                continue

            # 选择几个年龄点进行综合评估
            common_ages = []
            for _, weight_row in weight_df.iterrows():
                age = weight_row['年龄']
                # 查找身高表中是否有相同年龄
                height_row = height_df[height_df['年龄'] == age]
                if not height_row.empty and pd.notna(weight_row['P50']) and pd.notna(height_row.iloc[0]['P50']):
                    common_ages.append((age, weight_row, height_row.iloc[0]))

            # 随机选择几个年龄点
            selected_ages = random.sample(common_ages, min(5, len(common_ages)))

            for age, weight_row, height_row in selected_ages:
                if generated_count >= target_count:
                    break

                # 生成不同的体重身高组合
                weight_values = [weight_row['P25'], weight_row['P50'], weight_row['P75']]
                height_values = [height_row['P25'], height_row['P50'], height_row['P75']]

                for weight in weight_values:
                    for height in height_values:
                        if generated_count >= target_count:
                            break

                        if pd.isna(weight) or pd.isna(height) or weight <= 0 or height <= 0:
                            continue

                        bmi = self.calculate_bmi(weight, height)
                        if bmi is None:
                            continue

                        # 生成问题
                        questions = self.generate_comprehensive_questions(age, gender, weight, height, bmi)

                        for question in questions[:2]:  # 每个组合生成2个问题
                            if generated_count >= target_count:
                                break

                            # 生成综合评估
                            scenario = {
                                'age': age,
                                'gender': gender,
                                'weight': weight,
                                'height': height,
                                'bmi': bmi
                            }

                            assessment = self.generate_comprehensive_assessment(scenario)

                            sample = {
                                "instruction": question,
                                "input": "",
                                "output": assessment
                            }

                            self.dataset.append(sample)
                            generated_count += 1

                            print(f"已生成综合评估样本 {generated_count}/{target_count}")
                            time.sleep(self.request_interval)

        return generated_count

    def generate_comprehensive_questions(self, age: str, gender: str, weight: float, height: float, bmi: float) -> List[str]:
        """生成综合评估问题"""

        prompt = f"""
请为儿童综合生长发育评估生成3种不同的问题表述方式。

基础信息：
- 年龄：{age}
- 性别：{gender}童
- 体重：{weight}kg
- 身高：{height}cm
- BMI：{bmi}（可提及也可不提及）

要求：
1. 问题要涵盖体重和身高的综合评估
2. 可以是家长咨询、医生评估、体检报告等场景
3. 表述自然、专业
4. 每行一个问题，不要编号

示例格式：
请评估一个2岁男童体重12kg身高85cm的生长发育情况
2岁男宝宝体重12公斤、身高85厘米，发育正常吗？
男童2岁，体检显示体重12kg、身高85cm，请给出专业评估
"""

        response = self.call_qwen_api(prompt, max_tokens=400, temperature=0.8)
        if response:
            questions = [q.strip() for q in response.split('\n') if q.strip() and len(q.strip()) > 10]
            return questions[:3]

        # 备用问题
        return [f"请评估一个{age}的{gender}童体重{weight}kg身高{height}cm的生长发育情况"]

    def generate_head_circumference_samples(self, df: pd.DataFrame, table_name: str, max_samples: int = 30) -> int:
        """生成头围评估样本"""
        print(f"正在生成头围评估样本: {table_name}")

        gender = '男' if '男童' in table_name else '女'
        generated_count = 0

        # 选择有效数据行
        valid_rows = []
        for _, row in df.iterrows():
            percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
            valid = True
            for col in percentile_cols:
                if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                    valid = False
                    break
            if valid and pd.notna(row['年龄']):
                valid_rows.append(row)

        if len(valid_rows) == 0:
            return 0

        # 随机选择行生成样本
        sample_rows = random.sample(valid_rows, min(8, len(valid_rows)))

        for row in sample_rows:
            age = row['年龄']

            # 清理百分位数数据
            percentiles = {}
            for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                percentiles[col] = self._clean_numeric_value(row[col])

            # 生成不同的头围值
            test_values = [
                percentiles['P25'],  # 中下
                percentiles['P50'],  # 正常
                percentiles['P75'],  # 中上
            ]

            for value in test_values:
                if value is None or value <= 0:
                    continue

                assessment_level = self.get_growth_assessment(value, percentiles)

                # 构建场景信息
                scenario = {
                    'age': age,
                    'gender': gender,
                    'indicator': '头围',
                    'value': value,
                    'unit': 'cm',
                    'percentiles': percentiles,
                    'assessment_level': assessment_level
                }

                # 生成头围专用问题
                questions = self.generate_head_circumference_questions(scenario)

                for question in questions:
                    # 生成头围专用评估
                    assessment = self.generate_head_circumference_assessment(scenario)

                    sample = {
                        "instruction": question,
                        "input": "",
                        "output": assessment
                    }

                    self.dataset.append(sample)
                    generated_count += 1

                    print(f"已生成头围样本 {generated_count}")
                    time.sleep(self.request_interval)

        return generated_count

    def generate_head_circumference_questions(self, scenario: Dict[str, Any]) -> List[str]:
        """生成头围专用问题"""

        age = scenario['age']
        gender = scenario['gender']
        value = scenario['value']

        prompt = f"""
请为儿童头围评估生成2种不同的问题表述方式。

基础信息：
- 年龄：{age}
- 性别：{gender}童
- 头围：{value}cm

要求：
1. 问题要体现头围测量的重要性
2. 可以是家长关心、医生检查等场景
3. 表述自然、专业
4. 每行一个问题，不要编号

示例格式：
请评估一个6月龄男婴头围42cm的发育情况
6个月男宝宝头围42厘米，这个数值正常吗？
"""

        response = self.call_qwen_api(prompt, max_tokens=300, temperature=0.8)
        if response:
            questions = [q.strip() for q in response.split('\n') if q.strip() and len(q.strip()) > 10]
            return questions[:2]

        # 备用问题
        return [f"请评估一个{age}的{gender}童头围{value}cm的发育情况"]

    def generate_head_circumference_assessment(self, scenario: Dict[str, Any]) -> str:
        """生成头围专用评估"""

        age = scenario['age']
        gender = scenario['gender']
        value = scenario['value']
        percentiles = scenario['percentiles']
        assessment_level = scenario['assessment_level']

        prompt = f"""
作为儿科医生，请对以下儿童头围发育情况给出专业评估：

患儿信息：
- 年龄：{age}
- 性别：{gender}童
- 头围：{value}cm

参考标准（百分位数）：
- P3: {percentiles.get('P3', 'N/A')}cm
- P25: {percentiles.get('P25', 'N/A')}cm
- P50: {percentiles.get('P50', 'N/A')}cm
- P75: {percentiles.get('P75', 'N/A')}cm
- P97: {percentiles.get('P97', 'N/A')}cm

评估水平：{assessment_level}

请提供：
1. 头围发育评估结论
2. 头围的临床意义说明
3. 具体的建议和注意事项
4. 必要时说明可能的影响因素

要求：
- 语言专业但易懂
- 强调头围对脑发育的重要性
- 长度150-200字
- 不要使用markdown格式
"""

        response = self.call_qwen_api(prompt, max_tokens=600, temperature=0.6)
        if response:
            return response

        # 备用回答
        return f"根据0-3岁儿童头围发育标准，{age}的{gender}童头围{value}cm属于{assessment_level}水平。头围反映脑部发育状况，参考标准P50为{percentiles.get('P50', 'N/A')}cm。建议定期监测头围增长情况。"

def main():
    """主函数"""
    # 设置API密钥到环境变量
    api_key = "sk-5eba46fbcff649d5bf28313bc865de10"
    os.environ["QWEN_API_KEY"] = api_key

    excel_file = Config.FILE_PATHS["excel_file"]

    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return

    try:
        builder = LLMAssistedDatasetBuilder()
        builder.build_llm_assisted_dataset()
    except ValueError as e:
        print(f"配置错误: {e}")
    except Exception as e:
        print(f"构建数据集时出错: {e}")

if __name__ == "__main__":
    main()
