#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版婴幼儿体格生长监测数据集构建器
生成更丰富、更多样化的训练数据
"""

import pandas as pd
import json
import random
import numpy as np
from typing import Dict, List, Tuple, Any
import os
from datetime import datetime

class EnhancedGrowthDatasetBuilder:
    """增强版婴幼儿生长发育数据集构建器"""
    
    def __init__(self, excel_file: str):
        self.excel_file = excel_file
        self.data_tables = {}
        self.evaluation_methods = {}
        self.dataset = []
        
        # 预定义的模板和知识库
        self.growth_indicators = {
            '体重': {'unit': 'kg', 'description': '反映儿童营养状况和生长发育水平'},
            '身长/身高': {'unit': 'cm', 'description': '反映儿童骨骼发育和线性生长'},
            'BMI': {'unit': 'kg/m²', 'description': '反映儿童体重与身高的比例关系'},
            '头围': {'unit': 'cm', 'description': '反映儿童脑部发育状况'}
        }
        
        self.assessment_levels = {
            '上': {'description': '生长水平较高，需关注是否过度', 'advice': '建议定期监测，注意均衡营养'},
            '中上': {'description': '生长水平良好', 'advice': '继续保持良好的生活习惯'},
            '中': {'description': '生长水平正常', 'advice': '继续保持均衡营养和适量运动'},
            '中下': {'description': '生长水平偏低，需要关注', 'advice': '建议加强营养，定期监测'},
            '下': {'description': '生长水平明显偏低，需要干预', 'advice': '建议及时就医，查找原因并进行干预'}
        }
    
    def load_excel_data(self):
        """加载Excel文件中的所有数据表"""
        print("正在加载Excel数据...")
        
        xl = pd.ExcelFile(self.excel_file)
        
        # 加载评价方法表
        for sheet_name in xl.sheet_names:
            if '评价方法' in sheet_name:
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.evaluation_methods[sheet_name] = df
                print(f"已加载评价方法表: {sheet_name}")
        
        # 加载标准数据表
        for sheet_name in xl.sheet_names:
            if sheet_name.startswith('表 A.') or sheet_name.startswith('表 B.'):
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.data_tables[sheet_name] = df
                print(f"已加载数据表: {sheet_name} - 形状: {df.shape}")
    
    def generate_basic_knowledge_qa(self):
        """生成基础知识问答"""
        basic_qa = [
            {
                "instruction": "什么是儿童生长发育监测？",
                "input": "",
                "output": "儿童生长发育监测是通过定期测量儿童的体重、身长/身高、头围等体格指标，并与标准参考值进行比较，评估儿童生长发育状况的过程。这有助于及早发现生长发育异常，为健康干预提供依据。"
            },
            {
                "instruction": "儿童生长发育评价有哪些方法？",
                "input": "",
                "output": "主要有两种评价方法：1）百分位数法：将儿童的测量值与同年龄、同性别儿童的百分位数进行比较，P3-P97为正常范围；2）标准差法：计算儿童测量值与参考人群均值的标准差数，-2SD到+2SD为正常范围。"
            },
            {
                "instruction": "如何理解生长发育的百分位数？",
                "input": "",
                "output": "百分位数表示在同年龄、同性别的儿童中，有多少百分比的儿童的测量值低于该值。例如：P50表示中位数，P25表示有25%的儿童低于此值，P97表示有97%的儿童低于此值。P3-P97之间被认为是正常范围。"
            },
            {
                "instruction": "儿童体重评估的意义是什么？",
                "input": "",
                "output": f"体重是{self.growth_indicators['体重']['description']}的重要指标。通过体重评估可以：1）判断儿童营养状况；2）监测生长速度；3）及早发现营养不良或肥胖；4）指导喂养和营养干预。"
            },
            {
                "instruction": "为什么要监测儿童头围？",
                "input": "",
                "output": f"头围是{self.growth_indicators['头围']['description']}的重要指标。头围异常可能提示：1）脑发育异常；2）颅内压增高；3）遗传性疾病；4）营养问题。0-3岁是头围监测的关键期。"
            },
            {
                "instruction": "BMI在儿童生长评估中的作用是什么？",
                "input": "",
                "output": f"BMI（体重指数）{self.growth_indicators['BMI']['description']}，是评估儿童营养状况的重要指标。可以帮助：1）识别超重和肥胖；2）评估营养不良；3）监测体重变化趋势；4）指导营养干预。"
            }
        ]
        
        self.dataset.extend(basic_qa)
    
    def generate_assessment_scenarios(self):
        """生成评估场景问答"""
        scenarios = [
            {
                "instruction": "一个6月龄男婴，体重6.5kg，身长65cm，请进行生长评估",
                "input": "",
                "output": "根据7岁以下儿童生长发育标准：\n体重6.5kg：处于P10-P25之间，属于中下水平\n身长65cm：处于P25-P50之间，属于中等水平\n\n评估结论：该婴儿身长发育正常，但体重偏轻，建议：1）增加喂养频次；2）确保母乳或配方奶充足；3）定期监测体重增长；4）必要时咨询儿科医生。"
            },
            {
                "instruction": "一个2岁女童，体重15kg，身高85cm，BMI 20.8，如何评估？",
                "input": "",
                "output": "根据7岁以下儿童生长发育标准：\n体重15kg：超过P97，属于上水平\n身高85cm：处于P25-P50之间，属于中等水平\nBMI 20.8：超过P97，属于上水平\n\n评估结论：该女童存在超重问题，建议：1）调整饮食结构，减少高热量食物；2）增加体力活动；3）定期监测体重和BMI；4）咨询营养师制定合理饮食计划。"
            },
            {
                "instruction": "一个1岁男童，体重8kg，身长72cm，头围44cm，请评估发育状况",
                "input": "",
                "output": "根据7岁以下儿童生长发育标准：\n体重8kg：处于P3-P10之间，属于下水平\n身长72cm：处于P3-P10之间，属于下水平\n头围44cm：处于P10-P25之间，属于中下水平\n\n评估结论：该男童体重和身长均偏低，可能存在生长迟缓，建议：1）及时就医查找原因；2）评估营养摄入；3）排除疾病因素；4）制定个性化干预方案。"
            }
        ]
        
        self.dataset.extend(scenarios)
    
    def generate_guidance_samples(self):
        """生成指导建议样本"""
        guidance_samples = [
            {
                "instruction": "如何促进婴幼儿健康生长发育？",
                "input": "",
                "output": "促进婴幼儿健康生长发育的关键措施：\n1. 营养方面：0-6月纯母乳喂养，6月后合理添加辅食，保证营养均衡\n2. 睡眠方面：保证充足睡眠，新生儿16-20小时，1-3岁12-14小时\n3. 运动方面：适龄的体格活动，促进骨骼肌肉发育\n4. 定期监测：按时体检，监测生长指标\n5. 疾病预防：按时接种疫苗，预防感染性疾病"
            },
            {
                "instruction": "发现儿童生长发育异常时应该怎么办？",
                "input": "",
                "output": "发现儿童生长发育异常时的处理步骤：\n1. 及时就医：到儿科或儿童保健科就诊\n2. 详细评估：进行全面的生长发育评估\n3. 查找原因：排查营养、疾病、遗传等因素\n4. 制定方案：根据具体情况制定干预计划\n5. 定期随访：密切监测生长变化\n6. 家庭配合：家长积极配合治疗和护理"
            },
            {
                "instruction": "如何正确测量儿童的生长指标？",
                "input": "",
                "output": "正确测量儿童生长指标的方法：\n1. 体重测量：空腹、排便后，脱去外衣和鞋袜，使用精确的体重秤\n2. 身长/身高：2岁以下测身长（卧位），2岁以上测身高（立位），使用标准测量板\n3. 头围测量：用软尺绕头部最大周径测量，经过枕骨粗隆和眉弓上方\n4. 测量时间：建议固定时间，避免餐后立即测量\n5. 记录准确：精确到小数点后一位，建立生长曲线图"
            }
        ]
        
        self.dataset.extend(guidance_samples)
    
    def generate_case_studies(self):
        """生成案例研究"""
        cases = [
            {
                "instruction": "案例分析：3月龄男婴，出生体重3.2kg，现体重5.8kg，身长58cm，母乳喂养，请分析其生长状况",
                "input": "",
                "output": "案例分析：\n基本信息：3月龄男婴，出生体重正常，纯母乳喂养\n\n生长评估：\n- 体重5.8kg：处于P25-P50之间，属于中等水平\n- 身长58cm：处于P10-P25之间，属于中下水平\n- 体重增长：3个月增长2.6kg，平均每月增长867g，符合预期\n\n分析结论：\n该婴儿体重增长良好，但身长略偏低。建议：\n1. 继续母乳喂养，确保母亲营养充足\n2. 观察身长增长趋势\n3. 1个月后复查\n4. 如身长持续偏低，建议进一步评估"
            }
        ]
        
        self.dataset.extend(cases)
    
    def generate_comparative_analysis(self):
        """生成对比分析样本"""
        comparisons = [
            {
                "instruction": "比较分析：同为6月龄男婴，A婴体重7.5kg身长67cm，B婴体重6.8kg身长65cm，哪个发育更好？",
                "input": "",
                "output": "对比分析：\n\nA婴（体重7.5kg，身长67cm）：\n- 体重：处于P50-P75之间，中上水平\n- 身长：处于P50左右，中等水平\n- 体重身长比例协调\n\nB婴（体重6.8kg，身长65cm）：\n- 体重：处于P25-P50之间，中等水平\n- 身长：处于P25左右，中等偏下\n- 体重身长比例基本协调\n\n结论：A婴的生长发育状况相对更好，但B婴也在正常范围内。两者都需要继续观察生长趋势，重点关注生长速度而非单次测量值。"
            }
        ]
        
        self.dataset.extend(comparisons)
    
    def save_dataset(self, output_file: str = "enhanced_growth_dataset.jsonl"):
        """保存增强版数据集"""
        print(f"正在保存增强版数据集到 {output_file}...")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in self.dataset:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"数据集已保存，共 {len(self.dataset)} 条记录")
        
        # 同时保存为JSON格式
        json_file = output_file.replace('.jsonl', '.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset, f, ensure_ascii=False, indent=2)
        
        print(f"同时保存JSON格式到 {json_file}")
    
    def generate_statistics(self):
        """生成数据集统计信息"""
        print("\n=== 增强版数据集统计信息 ===")
        print(f"总样本数: {len(self.dataset)}")
        
        # 按类型统计
        instruction_types = {}
        for item in self.dataset:
            instruction = item['instruction']
            if '评估' in instruction or '分析' in instruction:
                key = '评估分析类'
            elif '如何' in instruction or '怎么' in instruction:
                key = '指导建议类'
            elif '什么是' in instruction or '为什么' in instruction:
                key = '基础知识类'
            elif '案例' in instruction or '比较' in instruction:
                key = '案例研究类'
            else:
                key = '其他'
            
            instruction_types[key] = instruction_types.get(key, 0) + 1
        
        for type_name, count in instruction_types.items():
            print(f"{type_name}: {count} 条")
    
    def build_enhanced_dataset(self):
        """构建增强版数据集"""
        print("开始构建增强版婴幼儿体格生长监测数据集...")
        
        # 加载数据
        self.load_excel_data()
        
        # 生成各类样本
        self.generate_basic_knowledge_qa()
        self.generate_assessment_scenarios()
        self.generate_guidance_samples()
        self.generate_case_studies()
        self.generate_comparative_analysis()
        
        # 打乱数据集
        random.shuffle(self.dataset)
        
        # 生成统计信息
        self.generate_statistics()
        
        # 保存数据集
        self.save_dataset()
        
        print("增强版数据集构建完成！")

def main():
    """主函数"""
    excel_file = "7岁以下儿童生长发育标准（卫健委.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return
    
    builder = EnhancedGrowthDatasetBuilder(excel_file)
    builder.build_enhanced_dataset()

if __name__ == "__main__":
    main()
