#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM辅助数据集构建器配置文件
"""

import os
from typing import Dict, Any

class Config:
    """配置类"""
    
    # Qwen API配置
    QWEN_API_KEY = os.getenv("QWEN_API_KEY", "")  # 从环境变量读取
    QWEN_BASE_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    QWEN_MODEL = "qwen-plus-2025-07-14"  # 可选: qwen-turbo, qwen-plus, qwen-max
    
    # API调用参数
    API_CONFIG = {
        "max_tokens": 1000,
        "temperature": 0.7,
        "top_p": 0.8,
        "request_interval": 1.0,  # 请求间隔（秒）
        "max_retries": 3,  # 最大重试次数
        "timeout": 30  # 超时时间（秒）
    }
    
    # 数据集生成配置
    DATASET_CONFIG = {
        "target_samples": 400,  # 目标生成样本数
        "questions_per_scenario": 3,  # 每个场景生成的问题数
        "train_ratio": 0.8,
        "val_ratio": 0.1,
        "test_ratio": 0.1
    }
    
    # 文件路径配置
    FILE_PATHS = {
        "excel_file": "7岁以下儿童生长发育标准（卫健委.xlsx",
        "output_dir": "llm_assisted_datasets",
        "log_file": "dataset_generation.log"
    }
    
    # 生长指标配置
    GROWTH_INDICATORS = {
        "体重": {"unit": "kg", "description": "反映儿童营养状况和生长发育水平"},
        "身高": {"unit": "cm", "description": "反映儿童骨骼发育和线性生长"},
        "身长": {"unit": "cm", "description": "反映婴幼儿骨骼发育和线性生长"},
        "BMI": {"unit": "", "description": "反映儿童体重与身高的比例关系"},
        "头围": {"unit": "cm", "description": "反映儿童脑部发育状况"}
    }
    
    # 评估水平配置
    ASSESSMENT_LEVELS = {
        "上": {"description": "生长水平较高", "advice": "建议定期监测，注意均衡营养"},
        "中上": {"description": "生长水平良好", "advice": "继续保持良好的生活习惯"},
        "中": {"description": "生长水平正常", "advice": "继续保持均衡营养和适量运动"},
        "中下": {"description": "生长水平偏低", "advice": "建议加强营养，定期监测"},
        "下": {"description": "生长水平明显偏低", "advice": "建议及时就医，查找原因并进行干预"}
    }
    
    # 知识问答主题
    KNOWLEDGE_TOPICS = [
        "儿童生长发育监测的重要性",
        "如何正确测量儿童体重",
        "如何正确测量儿童身高",
        "儿童BMI的意义和计算方法",
        "儿童头围测量的重要性",
        "生长发育迟缓的识别和处理",
        "儿童营养不良的预防",
        "促进儿童健康生长的方法",
        "儿童生长曲线的解读",
        "不同年龄段儿童的生长特点"
    ]
    
    # Prompt模板
    PROMPT_TEMPLATES = {
        "question_generation": """
请为儿童生长发育评估生成{count}种不同的问题表述方式。

基础信息：
- 年龄：{age}
- 性别：{gender}童
- 指标：{indicator}
- 数值：{value}{unit}

要求：
1. 每种表述都要自然、专业
2. 可以是家长咨询、医生评估、体检报告等不同场景
3. 表述方式要有变化，但核心信息保持一致
4. 每行一个问题，不要编号
5. 问题要简洁明确，适合实际应用场景

示例格式：
请评估一个6月龄男婴体重7.2kg的生长情况
6个月大的男宝宝体重7.2公斤，这个数值正常吗？
男婴6月龄，体检显示体重7.2kg，请给出专业评估
""",
        
        "assessment_generation": """
作为儿科医生，请对以下儿童生长发育情况给出专业评估：

患儿信息：
- 年龄：{age}
- 性别：{gender}童
- {indicator}：{value}{unit}

参考标准（百分位数）：
- P3: {p3}
- P10: {p10}
- P25: {p25}
- P50: {p50}
- P75: {p75}
- P90: {p90}
- P97: {p97}

评估水平：{assessment_level}

请提供：
1. 明确的评估结论
2. 参考标准说明
3. 具体的建议或注意事项
4. 必要时说明可能的原因

要求：
- 语言专业但易懂
- 结构清晰
- 长度适中（100-200字）
- 不要使用markdown格式
- 提供实用的建议
""",
        
        "knowledge_question": """
请生成一个关于'{topic}'的专业问题，适合家长或医护人员询问。

要求：
1. 问题要简洁明确
2. 具有实际应用价值
3. 适合不同知识背景的人群
4. 不要使用专业术语过多
5. 一句话表达完整

示例：
什么是儿童生长发育监测？
如何判断孩子的生长发育是否正常？
""",
        
        "guidance_content": """
作为儿童保健专家，请就"{topic}"这个主题提供专业的指导建议。

{context}

要求：
1. 内容专业、准确、实用
2. 结构清晰，要点明确
3. 适合家长理解和执行
4. 长度控制在150-300字
5. 不要使用markdown格式
6. 提供具体的、可操作的建议
7. 必要时说明注意事项

请提供系统性的指导内容。
"""
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置"""
        # 重新读取环境变量
        cls.QWEN_API_KEY = os.getenv("QWEN_API_KEY", "")

        if not cls.QWEN_API_KEY:
            print("错误：未设置QWEN_API_KEY")
            print("请设置环境变量：export QWEN_API_KEY='your_api_key'")
            return False

        if not os.path.exists(cls.FILE_PATHS["excel_file"]):
            print(f"错误：找不到Excel文件 {cls.FILE_PATHS['excel_file']}")
            return False

        return True
    
    @classmethod
    def get_api_headers(cls) -> Dict[str, str]:
        """获取API请求头"""
        return {
            "Authorization": f"Bearer {cls.QWEN_API_KEY}",
            "Content-Type": "application/json"
        }
    
    @classmethod
    def get_api_payload(cls, prompt: str, **kwargs) -> Dict[str, Any]:
        """获取API请求载荷"""
        config = cls.API_CONFIG.copy()
        config.update(kwargs)
        
        return {
            "model": cls.QWEN_MODEL,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "max_tokens": config["max_tokens"],
                "temperature": config["temperature"],
                "top_p": config["top_p"]
            }
        }
