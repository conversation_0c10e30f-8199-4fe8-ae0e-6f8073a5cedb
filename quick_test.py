#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 用于测试Qwen API连接和基本功能
"""

import os
import requests
import json
import time

def test_qwen_api():
    """测试Qwen API连接"""
    
    # 从环境变量获取API密钥
    api_key = os.getenv("QWEN_API_KEY")
    
    if not api_key:
        print("❌ 错误：未设置QWEN_API_KEY环境变量")
        print("请运行：export QWEN_API_KEY='your_api_key_here'")
        return False
    
    print("🔑 API密钥已设置")
    
    # API配置
    base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试请求
    test_prompt = "请简单介绍一下儿童生长发育监测的重要性，用一句话回答。"
    
    payload = {
        "model": "qwen-turbo",
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": test_prompt
                }
            ]
        },
        "parameters": {
            "max_tokens": 100,
            "temperature": 0.7
        }
    }
    
    try:
        print("🚀 正在测试API连接...")
        response = requests.post(base_url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if "output" in result and "text" in result["output"]:
            print("✅ API连接成功！")
            print(f"📝 测试回答: {result['output']['text'].strip()}")
            return True
        else:
            print("❌ API响应格式异常:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理响应时出错: {e}")
        return False

def generate_sample_questions():
    """生成示例问题"""
    
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        print("❌ 请先设置API密钥")
        return
    
    base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    prompt = """
请为儿童生长发育评估生成3种不同的问题表述方式。

基础信息：
- 年龄：6月
- 性别：男童
- 指标：体重
- 数值：7.2kg

要求：
1. 每种表述都要自然、专业
2. 可以是家长咨询、医生评估等不同场景
3. 每行一个问题，不要编号

示例格式：
请评估一个6月龄男婴体重7.2kg的生长情况
6个月大的男宝宝体重7.2公斤，这个数值正常吗？
"""
    
    payload = {
        "model": "qwen-turbo",
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        },
        "parameters": {
            "max_tokens": 300,
            "temperature": 0.8
        }
    }
    
    try:
        print("🎯 正在生成示例问题...")
        response = requests.post(base_url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if "output" in result and "text" in result["output"]:
            questions = result["output"]["text"].strip()
            print("✅ 生成的问题:")
            print("-" * 50)
            for i, q in enumerate(questions.split('\n'), 1):
                if q.strip():
                    print(f"{i}. {q.strip()}")
            print("-" * 50)
        else:
            print("❌ 生成失败")
            
    except Exception as e:
        print(f"❌ 生成问题时出错: {e}")

def generate_sample_assessment():
    """生成示例评估"""
    
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        print("❌ 请先设置API密钥")
        return
    
    base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    prompt = """
作为儿科医生，请对以下儿童生长发育情况给出专业评估：

患儿信息：
- 年龄：6月
- 性别：男童
- 体重：7.2kg

参考标准（百分位数）：
- P25: 6.4kg
- P50: 7.2kg  
- P75: 7.9kg

评估水平：中等

请提供：
1. 明确的评估结论
2. 参考标准说明
3. 具体的建议

要求：
- 语言专业但易懂
- 结构清晰
- 长度100-150字
"""
    
    payload = {
        "model": "qwen-turbo",
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        },
        "parameters": {
            "max_tokens": 500,
            "temperature": 0.6
        }
    }
    
    try:
        print("🏥 正在生成示例评估...")
        response = requests.post(base_url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if "output" in result and "text" in result["output"]:
            assessment = result["output"]["text"].strip()
            print("✅ 生成的评估:")
            print("-" * 50)
            print(assessment)
            print("-" * 50)
        else:
            print("❌ 生成失败")
            
    except Exception as e:
        print(f"❌ 生成评估时出错: {e}")

def main():
    """主函数"""
    print("🧪 Qwen API 快速测试工具")
    print("=" * 50)
    
    # 测试API连接
    if not test_qwen_api():
        print("\n❌ API连接测试失败，请检查配置")
        return
    
    print("\n" + "=" * 50)
    
    # 生成示例问题
    generate_sample_questions()
    
    print("\n" + "=" * 50)
    
    # 生成示例评估
    generate_sample_assessment()
    
    print("\n🎉 测试完成！如果以上功能正常，您可以开始使用完整的数据集构建器了。")
    print("\n📖 使用方法:")
    print("1. 确保Excel文件在当前目录")
    print("2. 运行: python llm_assisted_dataset_builder.py")

if __name__ == "__main__":
    main()
