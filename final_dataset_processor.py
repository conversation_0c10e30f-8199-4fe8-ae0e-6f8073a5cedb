#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终数据集处理器
将生成的数据集转换为适合微调训练的各种格式
"""

import json
import pandas as pd
import csv
import random
from typing import Dict, List, Any
import os
from datetime import datetime

class FinalDatasetProcessor:
    """最终数据集处理器"""
    
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.dataset = []
        self.train_set = []
        self.val_set = []
        self.test_set = []
    
    def load_dataset(self):
        """加载数据集"""
        print(f"正在加载数据集: {self.input_file}")
        
        if self.input_file.endswith('.jsonl'):
            with open(self.input_file, 'r', encoding='utf-8') as f:
                for line in f:
                    self.dataset.append(json.loads(line.strip()))
        elif self.input_file.endswith('.json'):
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.dataset = json.load(f)
        
        print(f"已加载 {len(self.dataset)} 条数据")
    
    def clean_and_standardize(self):
        """清理和标准化数据"""
        print("正在清理和标准化数据...")
        
        cleaned_dataset = []
        
        for item in self.dataset:
            # 确保所有必需字段存在
            if 'instruction' not in item or 'output' not in item:
                continue
            
            # 标准化格式
            cleaned_item = {
                'instruction': item['instruction'].strip(),
                'input': item.get('input', '').strip(),
                'output': item['output'].strip()
            }
            
            # 过滤掉内容过短的项目
            if len(cleaned_item['instruction']) < 5 or len(cleaned_item['output']) < 10:
                continue
            
            cleaned_dataset.append(cleaned_item)
        
        self.dataset = cleaned_dataset
        print(f"清理后剩余 {len(self.dataset)} 条数据")
    
    def split_dataset(self, train_ratio: float = 0.8, val_ratio: float = 0.1, test_ratio: float = 0.1):
        """分割数据集为训练集、验证集和测试集"""
        print("正在分割数据集...")
        
        # 打乱数据
        random.shuffle(self.dataset)
        
        total_size = len(self.dataset)
        train_size = int(total_size * train_ratio)
        val_size = int(total_size * val_ratio)
        
        self.train_set = self.dataset[:train_size]
        self.val_set = self.dataset[train_size:train_size + val_size]
        self.test_set = self.dataset[train_size + val_size:]
        
        print(f"训练集: {len(self.train_set)} 条")
        print(f"验证集: {len(self.val_set)} 条")
        print(f"测试集: {len(self.test_set)} 条")
    
    def save_jsonl_format(self, output_dir: str = "final_datasets"):
        """保存为JSONL格式（适合大多数微调框架）"""
        os.makedirs(output_dir, exist_ok=True)
        
        datasets = {
            'train': self.train_set,
            'validation': self.val_set,
            'test': self.test_set
        }
        
        for split_name, split_data in datasets.items():
            output_file = os.path.join(output_dir, f"{split_name}.jsonl")
            with open(output_file, 'w', encoding='utf-8') as f:
                for item in split_data:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
            print(f"已保存 {split_name} 集到: {output_file}")
    
    def save_alpaca_format(self, output_dir: str = "final_datasets"):
        """保存为Alpaca格式（适合Alpaca微调）"""
        os.makedirs(output_dir, exist_ok=True)
        
        datasets = {
            'train': self.train_set,
            'validation': self.val_set,
            'test': self.test_set
        }
        
        for split_name, split_data in datasets.items():
            output_file = os.path.join(output_dir, f"alpaca_{split_name}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(split_data, f, ensure_ascii=False, indent=2)
            print(f"已保存 Alpaca 格式 {split_name} 集到: {output_file}")
    
    def save_csv_format(self, output_dir: str = "final_datasets"):
        """保存为CSV格式（便于查看和分析）"""
        os.makedirs(output_dir, exist_ok=True)
        
        datasets = {
            'train': self.train_set,
            'validation': self.val_set,
            'test': self.test_set
        }
        
        for split_name, split_data in datasets.items():
            output_file = os.path.join(output_dir, f"{split_name}.csv")
            df = pd.DataFrame(split_data)
            df.to_csv(output_file, index=False, encoding='utf-8')
            print(f"已保存 CSV 格式 {split_name} 集到: {output_file}")
    
    def save_huggingface_format(self, output_dir: str = "final_datasets"):
        """保存为HuggingFace datasets格式"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建dataset_dict.json
        dataset_info = {
            "description": "婴幼儿体格生长监测数据集 - 用于微调支持体格生长监测和指导功能的LLM",
            "version": "1.0.0",
            "splits": {
                "train": {"num_examples": len(self.train_set)},
                "validation": {"num_examples": len(self.val_set)},
                "test": {"num_examples": len(self.test_set)}
            },
            "features": {
                "instruction": {"dtype": "string"},
                "input": {"dtype": "string"},
                "output": {"dtype": "string"}
            },
            "created_date": datetime.now().isoformat(),
            "source": "7岁以下儿童生长发育标准（卫健委）"
        }
        
        info_file = os.path.join(output_dir, "dataset_info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, ensure_ascii=False, indent=2)
        
        print(f"已保存数据集信息到: {info_file}")
    
    def save_chatglm_format(self, output_dir: str = "final_datasets"):
        """保存为ChatGLM微调格式"""
        os.makedirs(output_dir, exist_ok=True)
        
        # ChatGLM格式：每行一个对话
        datasets = {
            'train': self.train_set,
            'validation': self.val_set,
            'test': self.test_set
        }
        
        for split_name, split_data in datasets.items():
            output_file = os.path.join(output_dir, f"chatglm_{split_name}.jsonl")
            with open(output_file, 'w', encoding='utf-8') as f:
                for item in split_data:
                    # ChatGLM格式
                    chatglm_item = {
                        "conversations": [
                            {
                                "role": "user",
                                "content": item['instruction'] + ('\n' + item['input'] if item['input'] else '')
                            },
                            {
                                "role": "assistant", 
                                "content": item['output']
                            }
                        ]
                    }
                    f.write(json.dumps(chatglm_item, ensure_ascii=False) + '\n')
            print(f"已保存 ChatGLM 格式 {split_name} 集到: {output_file}")
    
    def generate_statistics(self):
        """生成数据集统计信息"""
        print("\n" + "="*50)
        print("最终数据集统计信息")
        print("="*50)
        
        print(f"总样本数: {len(self.dataset)}")
        print(f"训练集: {len(self.train_set)} 条 ({len(self.train_set)/len(self.dataset)*100:.1f}%)")
        print(f"验证集: {len(self.val_set)} 条 ({len(self.val_set)/len(self.dataset)*100:.1f}%)")
        print(f"测试集: {len(self.test_set)} 条 ({len(self.test_set)/len(self.dataset)*100:.1f}%)")
        
        # 分析指令类型分布
        instruction_types = {}
        for item in self.dataset:
            instruction = item['instruction']
            if '评估' in instruction:
                if '体重' in instruction:
                    key = '体重评估'
                elif '身高' in instruction or '身长' in instruction:
                    key = '身高评估'
                elif 'BMI' in instruction:
                    key = 'BMI评估'
                elif '头围' in instruction:
                    key = '头围评估'
                else:
                    key = '综合评估'
            elif '如何' in instruction or '怎么' in instruction:
                key = '指导建议'
            elif '什么是' in instruction or '为什么' in instruction:
                key = '基础知识'
            else:
                key = '其他'
            
            instruction_types[key] = instruction_types.get(key, 0) + 1
        
        print(f"\n指令类型分布:")
        for type_name, count in sorted(instruction_types.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(self.dataset) * 100
            print(f"  {type_name}: {count} 条 ({percentage:.1f}%)")
        
        # 分析文本长度
        instruction_lengths = [len(item['instruction']) for item in self.dataset]
        output_lengths = [len(item['output']) for item in self.dataset]
        
        print(f"\n文本长度统计:")
        print(f"  指令平均长度: {sum(instruction_lengths)/len(instruction_lengths):.1f} 字符")
        print(f"  输出平均长度: {sum(output_lengths)/len(output_lengths):.1f} 字符")
        print(f"  指令长度范围: {min(instruction_lengths)} - {max(instruction_lengths)} 字符")
        print(f"  输出长度范围: {min(output_lengths)} - {max(output_lengths)} 字符")
        
        print("="*50)
    
    def create_readme(self, output_dir: str = "final_datasets"):
        """创建README文件"""
        readme_content = f"""# 婴幼儿体格生长监测数据集

## 数据集描述

本数据集专门用于微调大语言模型，使其具备婴幼儿体格生长监测和指导功能。数据集基于国家卫健委发布的《7岁以下儿童生长发育标准》构建，包含体重、身高、BMI、头围等多个生长指标的评估和指导内容。

## 数据集统计

- **总样本数**: {len(self.dataset)} 条
- **训练集**: {len(self.train_set)} 条
- **验证集**: {len(self.val_set)} 条  
- **测试集**: {len(self.test_set)} 条

## 数据格式

每个样本包含以下字段：
- `instruction`: 用户指令/问题
- `input`: 额外输入信息（通常为空）
- `output`: 模型应该生成的回答

## 文件说明

### 标准格式
- `train.jsonl`: 训练集（JSONL格式）
- `validation.jsonl`: 验证集（JSONL格式）
- `test.jsonl`: 测试集（JSONL格式）

### 特定框架格式
- `alpaca_*.json`: Alpaca微调格式
- `chatglm_*.jsonl`: ChatGLM微调格式
- `*.csv`: CSV格式（便于查看）

### 元数据
- `dataset_info.json`: 数据集详细信息
- `README.md`: 本文件

## 使用方法

### 1. Alpaca微调
```python
# 使用alpaca_train.json进行微调
```

### 2. ChatGLM微调  
```python
# 使用chatglm_train.jsonl进行微调
```

### 3. 通用微调
```python
# 使用train.jsonl进行微调
```

## 数据集特点

1. **专业性强**: 基于权威医学标准构建
2. **覆盖全面**: 包含多个生长指标和年龄段
3. **实用性高**: 直接面向实际应用场景
4. **质量可靠**: 经过多轮验证和清理

## 注意事项

1. 本数据集仅用于研究和教育目的
2. 实际医疗应用需要专业医生指导
3. 模型输出不能替代专业医疗建议

## 版本信息

- **版本**: 1.0.0
- **创建日期**: {datetime.now().strftime('%Y-%m-%d')}
- **数据源**: 7岁以下儿童生长发育标准（卫健委）

## 联系信息

如有问题或建议，请联系数据集维护者。
"""
        
        readme_file = os.path.join(output_dir, "README.md")
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"已创建README文件: {readme_file}")
    
    def process_final_dataset(self):
        """处理最终数据集"""
        print("开始处理最终数据集...")
        
        # 加载和清理数据
        self.load_dataset()
        self.clean_and_standardize()
        
        # 分割数据集
        self.split_dataset()
        
        # 保存各种格式
        self.save_jsonl_format()
        self.save_alpaca_format()
        self.save_csv_format()
        self.save_huggingface_format()
        self.save_chatglm_format()
        
        # 生成统计信息和文档
        self.generate_statistics()
        self.create_readme()
        
        print("\n最终数据集处理完成！")

def main():
    """主函数"""
    # 使用质量最高的综合数据集
    input_file = "comprehensive_growth_dataset.json"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        return
    
    processor = FinalDatasetProcessor(input_file)
    processor.process_final_dataset()

if __name__ == "__main__":
    main()
