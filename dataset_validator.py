#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集质量验证器
用于验证生成的婴幼儿体格生长监测数据集的质量和一致性
"""

import json
import pandas as pd
import re
from typing import Dict, List, Tuple, Any
import os

class DatasetValidator:
    """数据集质量验证器"""
    
    def __init__(self, dataset_file: str):
        self.dataset_file = dataset_file
        self.dataset = []
        self.validation_results = {
            'total_samples': 0,
            'format_errors': [],
            'content_errors': [],
            'consistency_errors': [],
            'quality_scores': {}
        }
    
    def load_dataset(self):
        """加载数据集"""
        print(f"正在加载数据集: {self.dataset_file}")
        
        if self.dataset_file.endswith('.jsonl'):
            with open(self.dataset_file, 'r', encoding='utf-8') as f:
                for line in f:
                    self.dataset.append(json.loads(line.strip()))
        elif self.dataset_file.endswith('.json'):
            with open(self.dataset_file, 'r', encoding='utf-8') as f:
                self.dataset = json.load(f)
        
        self.validation_results['total_samples'] = len(self.dataset)
        print(f"已加载 {len(self.dataset)} 条数据")
    
    def validate_format(self):
        """验证数据格式"""
        print("正在验证数据格式...")
        
        required_fields = ['instruction', 'input', 'output']
        
        for i, item in enumerate(self.dataset):
            # 检查必需字段
            for field in required_fields:
                if field not in item:
                    self.validation_results['format_errors'].append({
                        'index': i,
                        'error': f'缺少必需字段: {field}',
                        'item': item
                    })
            
            # 检查字段类型
            for field in required_fields:
                if field in item and not isinstance(item[field], str):
                    self.validation_results['format_errors'].append({
                        'index': i,
                        'error': f'字段 {field} 应为字符串类型',
                        'item': item
                    })
            
            # 检查内容长度
            if 'instruction' in item and len(item['instruction']) < 5:
                self.validation_results['format_errors'].append({
                    'index': i,
                    'error': 'instruction 内容过短',
                    'item': item
                })
            
            if 'output' in item and len(item['output']) < 10:
                self.validation_results['format_errors'].append({
                    'index': i,
                    'error': 'output 内容过短',
                    'item': item
                })
    
    def validate_content_accuracy(self):
        """验证内容准确性"""
        print("正在验证内容准确性...")
        
        for i, item in enumerate(self.dataset):
            instruction = item.get('instruction', '')
            output = item.get('output', '')
            
            # 检查数值评估的一致性
            if '评估' in instruction and '体重' in instruction:
                self._validate_weight_assessment(i, instruction, output)
            elif '评估' in instruction and ('身高' in instruction or '身长' in instruction):
                self._validate_height_assessment(i, instruction, output)
            elif '评估' in instruction and 'BMI' in instruction:
                self._validate_bmi_assessment(i, instruction, output)
            elif '评估' in instruction and '头围' in instruction:
                self._validate_head_circumference_assessment(i, instruction, output)
    
    def _validate_weight_assessment(self, index: int, instruction: str, output: str):
        """验证体重评估的准确性"""
        # 提取指令中的数值信息
        weight_match = re.search(r'体重(\d+\.?\d*)kg', instruction)
        age_match = re.search(r'(\d+(?:岁\d*)?月?|\d+岁)', instruction)
        gender_match = re.search(r'(男|女)童', instruction)
        
        if not weight_match or not age_match or not gender_match:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '无法从指令中提取完整的评估信息',
                'instruction': instruction
            })
            return
        
        weight = float(weight_match.group(1))
        age = age_match.group(1)
        gender = gender_match.group(1)
        
        # 检查输出中的评估结果
        if '属于' not in output:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '输出中缺少评估结果',
                'output': output
            })
        
        # 检查是否包含参考标准
        if 'P50' not in output or 'P25-P75' not in output:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '输出中缺少参考标准信息',
                'output': output
            })
    
    def _validate_height_assessment(self, index: int, instruction: str, output: str):
        """验证身高评估的准确性"""
        height_match = re.search(r'身高(\d+\.?\d*)cm', instruction)
        if not height_match:
            height_match = re.search(r'身长(\d+\.?\d*)cm', instruction)
        
        if not height_match:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '无法从指令中提取身高信息',
                'instruction': instruction
            })
            return
        
        # 检查基本格式
        if '属于' not in output or 'P50' not in output:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '身高评估输出格式不完整',
                'output': output
            })
    
    def _validate_bmi_assessment(self, index: int, instruction: str, output: str):
        """验证BMI评估的准确性"""
        bmi_match = re.search(r'BMI(\d+\.?\d*)', instruction)
        
        if not bmi_match:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '无法从指令中提取BMI信息',
                'instruction': instruction
            })
            return
        
        # 检查基本格式
        if '属于' not in output or 'P50' not in output:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': 'BMI评估输出格式不完整',
                'output': output
            })
    
    def _validate_head_circumference_assessment(self, index: int, instruction: str, output: str):
        """验证头围评估的准确性"""
        hc_match = re.search(r'头围(\d+\.?\d*)cm', instruction)
        
        if not hc_match:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '无法从指令中提取头围信息',
                'instruction': instruction
            })
            return
        
        # 检查基本格式和特殊说明
        if '属于' not in output or 'P50' not in output:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '头围评估输出格式不完整',
                'output': output
            })
        
        if '脑部发育' not in output and '头围反映' not in output:
            self.validation_results['content_errors'].append({
                'index': index,
                'error': '头围评估缺少发育意义说明',
                'output': output
            })
    
    def validate_consistency(self):
        """验证数据一致性"""
        print("正在验证数据一致性...")
        
        # 检查重复项
        instructions = [item['instruction'] for item in self.dataset]
        unique_instructions = set(instructions)
        
        if len(instructions) != len(unique_instructions):
            duplicates = len(instructions) - len(unique_instructions)
            self.validation_results['consistency_errors'].append({
                'error': f'发现 {duplicates} 个重复的指令',
                'count': duplicates
            })
        
        # 检查评估水平的一致性
        assessment_levels = ['上', '中上', '中', '中下', '下']
        for i, item in enumerate(self.dataset):
            output = item.get('output', '')
            if '属于' in output:
                found_level = False
                for level in assessment_levels:
                    if f'属于{level}水平' in output:
                        found_level = True
                        break
                
                if not found_level:
                    self.validation_results['consistency_errors'].append({
                        'index': i,
                        'error': '评估水平格式不标准',
                        'output': output
                    })
    
    def calculate_quality_scores(self):
        """计算质量分数"""
        print("正在计算质量分数...")
        
        total_samples = self.validation_results['total_samples']
        format_errors = len(self.validation_results['format_errors'])
        content_errors = len(self.validation_results['content_errors'])
        consistency_errors = len(self.validation_results['consistency_errors'])
        
        # 格式质量分数 (0-100)
        format_score = max(0, 100 - (format_errors / total_samples * 100)) if total_samples > 0 else 0
        
        # 内容质量分数 (0-100)
        content_score = max(0, 100 - (content_errors / total_samples * 100)) if total_samples > 0 else 0
        
        # 一致性分数 (0-100)
        consistency_score = max(0, 100 - (consistency_errors / total_samples * 100)) if total_samples > 0 else 0
        
        # 总体质量分数
        overall_score = (format_score + content_score + consistency_score) / 3
        
        self.validation_results['quality_scores'] = {
            'format_score': round(format_score, 2),
            'content_score': round(content_score, 2),
            'consistency_score': round(consistency_score, 2),
            'overall_score': round(overall_score, 2)
        }
    
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*50)
        print("数据集质量验证报告")
        print("="*50)
        
        print(f"数据集文件: {self.dataset_file}")
        print(f"总样本数: {self.validation_results['total_samples']}")
        
        print(f"\n质量分数:")
        scores = self.validation_results['quality_scores']
        print(f"  格式质量: {scores['format_score']}/100")
        print(f"  内容质量: {scores['content_score']}/100")
        print(f"  一致性: {scores['consistency_score']}/100")
        print(f"  总体质量: {scores['overall_score']}/100")
        
        print(f"\n错误统计:")
        print(f"  格式错误: {len(self.validation_results['format_errors'])} 个")
        print(f"  内容错误: {len(self.validation_results['content_errors'])} 个")
        print(f"  一致性错误: {len(self.validation_results['consistency_errors'])} 个")
        
        # 显示前几个错误示例
        if self.validation_results['format_errors']:
            print(f"\n格式错误示例 (前3个):")
            for error in self.validation_results['format_errors'][:3]:
                print(f"  - 索引 {error['index']}: {error['error']}")
        
        if self.validation_results['content_errors']:
            print(f"\n内容错误示例 (前3个):")
            for error in self.validation_results['content_errors'][:3]:
                print(f"  - 索引 {error['index']}: {error['error']}")
        
        if self.validation_results['consistency_errors']:
            print(f"\n一致性错误示例:")
            for error in self.validation_results['consistency_errors']:
                print(f"  - {error['error']}")
        
        # 质量评级
        overall_score = scores['overall_score']
        if overall_score >= 90:
            grade = "优秀"
        elif overall_score >= 80:
            grade = "良好"
        elif overall_score >= 70:
            grade = "中等"
        elif overall_score >= 60:
            grade = "及格"
        else:
            grade = "需要改进"
        
        print(f"\n总体评级: {grade}")
        print("="*50)
    
    def save_validation_report(self, output_file: str = "validation_report.json"):
        """保存验证报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, ensure_ascii=False, indent=2)
        print(f"验证报告已保存到: {output_file}")
    
    def validate_dataset(self):
        """执行完整的数据集验证"""
        self.load_dataset()
        self.validate_format()
        self.validate_content_accuracy()
        self.validate_consistency()
        self.calculate_quality_scores()
        self.generate_report()
        self.save_validation_report()

def main():
    """主函数"""
    dataset_files = [
        "comprehensive_growth_dataset.json",
        "enhanced_growth_dataset.json",
        "growth_monitoring_dataset.json"
    ]
    
    for dataset_file in dataset_files:
        if os.path.exists(dataset_file):
            print(f"\n正在验证数据集: {dataset_file}")
            validator = DatasetValidator(dataset_file)
            validator.validate_dataset()
        else:
            print(f"数据集文件不存在: {dataset_file}")

if __name__ == "__main__":
    main()
