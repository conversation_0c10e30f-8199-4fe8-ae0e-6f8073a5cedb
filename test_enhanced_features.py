#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强功能脚本 - 验证头围数据和BMI计算功能
"""

import os
import sys
import json

# 设置API密钥
os.environ["QWEN_API_KEY"] = "sk-5eba46fbcff649d5bf28313bc865de10"

from llm_assisted_dataset_builder import LLMAssistedDatasetBuilder
from config import Config

def test_bmi_calculation():
    """测试BMI计算功能"""
    print("🧮 测试BMI计算功能...")
    
    builder = LLMAssistedDatasetBuilder()
    
    # 测试用例
    test_cases = [
        (10, 80, 15.6),  # 10kg, 80cm -> 15.6
        (15, 100, 15.0), # 15kg, 100cm -> 15.0
        (20, 120, 13.9), # 20kg, 120cm -> 13.9
    ]
    
    for weight, height, expected in test_cases:
        result = builder.calculate_bmi(weight, height)
        print(f"体重{weight}kg, 身高{height}cm -> BMI: {result} (期望: {expected})")
        assert abs(result - expected) < 0.1, f"BMI计算错误: {result} != {expected}"
    
    print("✅ BMI计算功能测试通过")

def test_comprehensive_question_generation():
    """测试综合问题生成"""
    print("\n🎯 测试综合问题生成...")
    
    builder = LLMAssistedDatasetBuilder()
    
    questions = builder.generate_comprehensive_questions("2岁", "男", 12.0, 85.0, 16.6)
    
    print("生成的综合评估问题:")
    for i, q in enumerate(questions, 1):
        print(f"{i}. {q}")
    
    assert len(questions) > 0, "未生成任何问题"
    print("✅ 综合问题生成测试通过")

def test_head_circumference_questions():
    """测试头围问题生成"""
    print("\n👶 测试头围问题生成...")
    
    builder = LLMAssistedDatasetBuilder()
    
    scenario = {
        'age': '6月',
        'gender': '男',
        'value': 42.0
    }
    
    questions = builder.generate_head_circumference_questions(scenario)
    
    print("生成的头围评估问题:")
    for i, q in enumerate(questions, 1):
        print(f"{i}. {q}")
    
    assert len(questions) > 0, "未生成任何头围问题"
    print("✅ 头围问题生成测试通过")

def test_comprehensive_assessment():
    """测试综合评估生成"""
    print("\n🏥 测试综合评估生成...")
    
    builder = LLMAssistedDatasetBuilder()
    
    scenario = {
        'age': '2岁',
        'gender': '男',
        'weight': 12.0,
        'height': 85.0,
        'bmi': 16.6
    }
    
    assessment = builder.generate_comprehensive_assessment(scenario)
    
    print("生成的综合评估:")
    print("-" * 50)
    print(assessment)
    print("-" * 50)
    
    assert len(assessment) > 50, "评估内容过短"
    assert "BMI" in assessment, "评估中未包含BMI信息"
    print("✅ 综合评估生成测试通过")

def test_head_circumference_assessment():
    """测试头围评估生成"""
    print("\n🧠 测试头围评估生成...")
    
    builder = LLMAssistedDatasetBuilder()
    
    scenario = {
        'age': '6月',
        'gender': '男',
        'value': 42.0,
        'percentiles': {'P3': 40.5, 'P25': 41.5, 'P50': 42.0, 'P75': 42.5, 'P97': 43.5},
        'assessment_level': '中'
    }
    
    assessment = builder.generate_head_circumference_assessment(scenario)
    
    print("生成的头围评估:")
    print("-" * 50)
    print(assessment)
    print("-" * 50)
    
    assert len(assessment) > 50, "头围评估内容过短"
    assert "头围" in assessment, "评估中未包含头围信息"
    assert "脑" in assessment or "发育" in assessment, "评估中未强调脑发育重要性"
    print("✅ 头围评估生成测试通过")

def test_data_loading():
    """测试数据加载"""
    print("\n📊 测试数据加载...")
    
    builder = LLMAssistedDatasetBuilder()
    builder.load_excel_data()
    
    print(f"已加载 {len(builder.data_tables)} 个数据表")
    
    # 检查是否包含头围数据表
    head_circumference_tables = [name for name in builder.data_tables.keys() if '头围' in name]
    print(f"头围数据表: {head_circumference_tables}")
    
    assert len(head_circumference_tables) > 0, "未找到头围数据表"
    print("✅ 数据加载测试通过")

def generate_sample_dataset():
    """生成小规模示例数据集"""
    print("\n🚀 生成小规模示例数据集...")
    
    # 临时修改配置，生成少量样本用于测试
    original_target = Config.DATASET_CONFIG["target_samples"]
    Config.DATASET_CONFIG["target_samples"] = 10
    
    try:
        builder = LLMAssistedDatasetBuilder()
        builder.load_excel_data()
        
        # 生成综合评估样本
        comprehensive_count = builder.generate_comprehensive_samples(3)
        print(f"生成了 {comprehensive_count} 个综合评估样本")
        
        # 生成头围样本
        head_tables = {name: df for name, df in builder.data_tables.items() if '头围' in name}
        head_count = 0
        for table_name, df in head_tables.items():
            head_count += builder.generate_head_circumference_samples(df, table_name)
            if head_count >= 3:  # 限制数量
                break
        
        print(f"生成了 {head_count} 个头围评估样本")
        
        # 保存示例数据集
        if builder.dataset:
            with open("enhanced_sample_dataset.json", "w", encoding="utf-8") as f:
                json.dump(builder.dataset, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 示例数据集已保存，共 {len(builder.dataset)} 个样本")
            
            # 显示样本
            print("\n📝 样本预览:")
            for i, sample in enumerate(builder.dataset[:3], 1):
                print(f"\n样本 {i}:")
                print(f"问题: {sample['instruction']}")
                print(f"回答: {sample['output'][:100]}...")
        
    finally:
        # 恢复原始配置
        Config.DATASET_CONFIG["target_samples"] = original_target

def main():
    """主函数"""
    print("🧪 增强功能测试套件")
    print("=" * 60)
    
    try:
        # 基础功能测试
        test_bmi_calculation()
        test_data_loading()
        
        # LLM功能测试
        test_comprehensive_question_generation()
        test_head_circumference_questions()
        test_comprehensive_assessment()
        test_head_circumference_assessment()
        
        # 生成示例数据集
        generate_sample_dataset()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！增强功能工作正常")
        print("\n📋 新功能总结:")
        print("✅ BMI自动计算")
        print("✅ 综合评估（体重+身高+BMI）")
        print("✅ 头围专项评估")
        print("✅ 多样化问题生成")
        print("✅ 专业医学评估")
        
        print("\n🚀 现在可以运行完整的数据集构建器:")
        print("python llm_assisted_dataset_builder.py")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
