# LLM辅助婴幼儿体格生长监测数据集构建使用说明

## 概述

本项目提供了一个基于Qwen API的LLM辅助数据集构建器，可以生成更加多样化、自然的婴幼儿体格生长监测训练数据。相比于预设模板的方式，LLM辅助生成能够：

- 🎯 **提高多样性**: 生成更多样化的问题表述和回答方式
- 🧠 **增强自然性**: 生成更符合真实对话场景的内容
- 📈 **扩大规模**: 快速生成大量高质量训练样本
- 🔄 **持续优化**: 通过调整prompt不断改进生成质量

## 文件结构

```
├── llm_assisted_dataset_builder.py    # 主要构建器
├── config.py                          # 配置文件
├── LLM辅助数据集构建使用说明.md       # 本文件
└── 7岁以下儿童生长发育标准（卫健委.xlsx  # 数据源
```

## 环境准备

### 1. 安装依赖

```bash
pip install pandas requests openpyxl
```

### 2. 获取Qwen API密钥

1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 注册并创建应用
3. 获取API密钥

### 3. 设置环境变量

**方法一：命令行设置**
```bash
export QWEN_API_KEY="your_actual_api_key_here"
```

**方法二：在代码中设置**
```python
import os
os.environ["QWEN_API_KEY"] = "your_actual_api_key_here"
```

**方法三：创建.env文件**
```bash
echo "QWEN_API_KEY=your_actual_api_key_here" > .env
```

## 配置说明

### 主要配置项 (config.py)

```python
# API配置
QWEN_MODEL = "qwen-turbo"  # 可选: qwen-turbo, qwen-plus, qwen-max
API_CONFIG = {
    "max_tokens": 1000,
    "temperature": 0.7,
    "request_interval": 1.0,  # 请求间隔，避免频率限制
    "max_retries": 3
}

# 数据集配置
DATASET_CONFIG = {
    "target_samples": 200,  # 目标生成样本数
    "questions_per_scenario": 3,  # 每个场景生成的问题数
}
```

### 模型选择建议

- **qwen-turbo**: 速度快，成本低，适合大批量生成
- **qwen-plus**: 平衡性能和成本，推荐使用
- **qwen-max**: 质量最高，成本较高，适合精品数据集

## 使用方法

### 1. 基本使用

```python
from llm_assisted_dataset_builder import LLMAssistedDatasetBuilder

# 创建构建器实例
builder = LLMAssistedDatasetBuilder()

# 构建数据集
builder.build_llm_assisted_dataset()
```

### 2. 命令行使用

```bash
python llm_assisted_dataset_builder.py
```

### 3. 自定义配置

```python
# 修改config.py中的配置
Config.DATASET_CONFIG["target_samples"] = 500  # 生成500个样本
Config.API_CONFIG["temperature"] = 0.8  # 提高创造性
```

## 生成的数据类型

### 1. 评估问答对
- **多样化问题**: 家长咨询、医生评估、体检报告等不同场景
- **专业回答**: 基于标准数据的专业评估和建议

示例：
```json
{
  "instruction": "6个月大的男宝宝体重7.2公斤，这个数值正常吗？",
  "input": "",
  "output": "根据7岁以下儿童生长发育标准，6月龄男婴体重7.2kg处于P50-P75之间，属于中上水平。这个体重是正常的，说明宝宝营养状况良好。建议继续保持均衡喂养，定期监测生长发育情况。"
}
```

### 2. 知识问答
- **基础知识**: 生长发育监测的重要概念
- **实用指导**: 测量方法、注意事项等

### 3. 案例分析
- **综合评估**: 多指标综合分析
- **个性化建议**: 针对性的指导方案

## 质量控制

### 1. 多重验证
- API响应格式验证
- 内容长度和质量检查
- 重复内容过滤

### 2. 错误处理
- 自动重试机制
- 降级策略（API失败时使用备用模板）
- 详细的日志记录

### 3. 人工审核
建议对生成的数据进行人工抽查，确保：
- 医学知识的准确性
- 建议的合理性
- 语言表达的自然性

## 成本控制

### 1. API调用优化
- 设置合理的请求间隔
- 使用批量处理
- 选择合适的模型

### 2. 成本估算
以qwen-turbo为例：
- 每1000个token约0.002元
- 生成200个样本预计成本：5-10元

### 3. 节省成本的建议
- 优化prompt长度
- 使用缓存避免重复调用
- 分批次生成，及时验证质量

## 常见问题

### Q1: API调用失败怎么办？
**A**: 检查以下几点：
1. API密钥是否正确设置
2. 网络连接是否正常
3. 是否超出API调用频率限制
4. 账户余额是否充足

### Q2: 生成的内容质量不理想？
**A**: 可以尝试：
1. 调整temperature参数（0.6-0.8）
2. 优化prompt模板
3. 使用更高级的模型（qwen-plus/qwen-max）
4. 增加人工审核环节

### Q3: 如何提高生成速度？
**A**: 
1. 减少request_interval
2. 使用qwen-turbo模型
3. 并行处理（需要注意API限制）

### Q4: 生成的数据如何验证？
**A**: 
1. 使用内置的数据验证器
2. 人工抽查关键样本
3. 与专业医生确认内容准确性

## 扩展开发

### 1. 添加新的数据类型
```python
def generate_new_type_samples(self):
    """生成新类型的样本"""
    # 实现新的生成逻辑
    pass
```

### 2. 自定义prompt模板
在config.py中添加新的模板：
```python
PROMPT_TEMPLATES["new_template"] = """
你的自定义prompt模板
"""
```

### 3. 集成其他LLM
修改API调用部分，支持其他模型：
```python
def call_other_llm_api(self, prompt: str):
    # 实现其他LLM的API调用
    pass
```

## 最佳实践

### 1. 数据生成策略
- 先小批量测试，验证质量后再大规模生成
- 定期检查生成内容的多样性
- 保持训练数据的平衡性

### 2. 质量保证
- 设置合理的温度参数
- 使用多轮生成和筛选
- 建立质量评估标准

### 3. 成本管理
- 监控API使用量
- 优化prompt效率
- 合理选择模型规格

## 技术支持

如果在使用过程中遇到问题，可以：

1. 查看日志文件 `dataset_generation.log`
2. 检查配置文件设置
3. 参考错误信息进行调试
4. 联系技术支持

## 更新日志

- **v1.0**: 初始版本，支持基本的LLM辅助生成
- **v1.1**: 添加配置文件支持，优化错误处理
- **v1.2**: 增加多种prompt模板，提升生成质量

---

**注意**: 本工具生成的数据仅用于研究和教育目的，实际医疗应用需要专业医生指导。
