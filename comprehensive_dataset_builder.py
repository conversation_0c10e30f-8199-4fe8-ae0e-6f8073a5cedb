#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合版婴幼儿体格生长监测数据集构建器
结合原始数据和增强样本，生成完整的训练数据集
"""

import pandas as pd
import json
import random
import numpy as np
from typing import Dict, List, Tuple, Any
import os
from datetime import datetime

class ComprehensiveGrowthDatasetBuilder:
    """综合版婴幼儿生长发育数据集构建器"""
    
    def __init__(self, excel_file: str):
        self.excel_file = excel_file
        self.data_tables = {}
        self.evaluation_methods = {}
        self.dataset = []
        
        # 生长指标信息
        self.growth_indicators = {
            '体重': {'unit': 'kg', 'description': '反映儿童营养状况和生长发育水平'},
            '身长/身高': {'unit': 'cm', 'description': '反映儿童骨骼发育和线性生长'},
            'BMI': {'unit': 'kg/m²', 'description': '反映儿童体重与身高的比例关系'},
            '头围': {'unit': 'cm', 'description': '反映儿童脑部发育状况'}
        }
        
        # 评估水平描述
        self.assessment_levels = {
            '上': {'description': '生长水平较高', 'advice': '建议定期监测，注意均衡营养'},
            '中上': {'description': '生长水平良好', 'advice': '继续保持良好的生活习惯'},
            '中': {'description': '生长水平正常', 'advice': '继续保持均衡营养和适量运动'},
            '中下': {'description': '生长水平偏低', 'advice': '建议加强营养，定期监测'},
            '下': {'description': '生长水平明显偏低', 'advice': '建议及时就医，查找原因并进行干预'}
        }
    
    def load_excel_data(self):
        """加载Excel文件中的所有数据表"""
        print("正在加载Excel数据...")
        
        xl = pd.ExcelFile(self.excel_file)
        
        # 加载评价方法表
        for sheet_name in xl.sheet_names:
            if '评价方法' in sheet_name:
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.evaluation_methods[sheet_name] = df
                print(f"已加载评价方法表: {sheet_name}")
        
        # 加载标准数据表
        for sheet_name in xl.sheet_names:
            if sheet_name.startswith('表 A.') or sheet_name.startswith('表 B.'):
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.data_tables[sheet_name] = df
                print(f"已加载数据表: {sheet_name} - 形状: {df.shape}")
    
    def parse_age_column(self, age_str: str) -> Tuple[int, str]:
        """解析年龄列，返回数值和单位"""
        if pd.isna(age_str):
            return None, None
        
        age_str = str(age_str).strip()
        
        # 处理复合年龄格式，如"1岁3月"
        if '岁' in age_str and '月' in age_str:
            parts = age_str.split('岁')
            years = int(parts[0])
            months_part = parts[1].replace('月', '').strip()
            months = int(months_part) if months_part else 0
            total_months = years * 12 + months
            return total_months, '月'
        elif '月' in age_str:
            age_num = int(age_str.replace('月', ''))
            return age_num, '月'
        elif '岁' in age_str:
            age_num = float(age_str.replace('岁', ''))
            return int(age_num * 12), '月'  # 转换为月
        return None, None
    
    def _clean_numeric_value(self, value):
        """清理数值，处理字符串和NaN"""
        if pd.isna(value):
            return None
        if isinstance(value, str):
            # 处理类似"10. 1"这样的格式
            value = value.replace(' ', '').replace('.', '.')
            try:
                return float(value)
            except:
                return None
        return float(value)
    
    def get_growth_assessment(self, value: float, percentiles: Dict[str, float], 
                            assessment_type: str = 'percentile') -> str:
        """根据百分位数或标准差评估生长水平"""
        if assessment_type == 'percentile':
            if value >= percentiles.get('P97', float('inf')):
                return '上'
            elif value >= percentiles.get('P75', float('inf')):
                return '中上'
            elif value >= percentiles.get('P25', float('inf')):
                return '中'
            elif value >= percentiles.get('P10', float('inf')):
                return '中下'
            else:
                return '下'
        return '中'
    
    def generate_basic_knowledge_qa(self):
        """生成基础知识问答"""
        basic_qa = [
            {
                "instruction": "什么是儿童生长发育监测？",
                "input": "",
                "output": "儿童生长发育监测是通过定期测量儿童的体重、身长/身高、头围等体格指标，并与标准参考值进行比较，评估儿童生长发育状况的过程。这有助于及早发现生长发育异常，为健康干预提供依据。"
            },
            {
                "instruction": "儿童生长发育评价有哪些方法？",
                "input": "",
                "output": "主要有两种评价方法：1）百分位数法：将儿童的测量值与同年龄、同性别儿童的百分位数进行比较，P3-P97为正常范围；2）标准差法：计算儿童测量值与参考人群均值的标准差数，-2SD到+2SD为正常范围。"
            },
            {
                "instruction": "如何理解生长发育的百分位数？",
                "input": "",
                "output": "百分位数表示在同年龄、同性别的儿童中，有多少百分比的儿童的测量值低于该值。例如：P50表示中位数，P25表示有25%的儿童低于此值，P97表示有97%的儿童低于此值。P3-P97之间被认为是正常范围。"
            },
            {
                "instruction": "儿童体重评估的意义是什么？",
                "input": "",
                "output": f"体重是{self.growth_indicators['体重']['description']}的重要指标。通过体重评估可以：1）判断儿童营养状况；2）监测生长速度；3）及早发现营养不良或肥胖；4）指导喂养和营养干预。"
            },
            {
                "instruction": "为什么要监测儿童头围？",
                "input": "",
                "output": f"头围是{self.growth_indicators['头围']['description']}的重要指标。头围异常可能提示：1）脑发育异常；2）颅内压增高；3）遗传性疾病；4）营养问题。0-3岁是头围监测的关键期。"
            },
            {
                "instruction": "BMI在儿童生长评估中的作用是什么？",
                "input": "",
                "output": f"BMI（体重指数）{self.growth_indicators['BMI']['description']}，是评估儿童营养状况的重要指标。可以帮助：1）识别超重和肥胖；2）评估营养不良；3）监测体重变化趋势；4）指导营养干预。"
            },
            {
                "instruction": "如何正确测量儿童的生长指标？",
                "input": "",
                "output": "正确测量儿童生长指标的方法：\n1. 体重测量：空腹、排便后，脱去外衣和鞋袜，使用精确的体重秤\n2. 身长/身高：2岁以下测身长（卧位），2岁以上测身高（立位），使用标准测量板\n3. 头围测量：用软尺绕头部最大周径测量，经过枕骨粗隆和眉弓上方\n4. 测量时间：建议固定时间，避免餐后立即测量\n5. 记录准确：精确到小数点后一位，建立生长曲线图"
            },
            {
                "instruction": "如何促进婴幼儿健康生长发育？",
                "input": "",
                "output": "促进婴幼儿健康生长发育的关键措施：\n1. 营养方面：0-6月纯母乳喂养，6月后合理添加辅食，保证营养均衡\n2. 睡眠方面：保证充足睡眠，新生儿16-20小时，1-3岁12-14小时\n3. 运动方面：适龄的体格活动，促进骨骼肌肉发育\n4. 定期监测：按时体检，监测生长指标\n5. 疾病预防：按时接种疫苗，预防感染性疾病"
            },
            {
                "instruction": "发现儿童生长发育异常时应该怎么办？",
                "input": "",
                "output": "发现儿童生长发育异常时的处理步骤：\n1. 及时就医：到儿科或儿童保健科就诊\n2. 详细评估：进行全面的生长发育评估\n3. 查找原因：排查营养、疾病、遗传等因素\n4. 制定方案：根据具体情况制定干预计划\n5. 定期随访：密切监测生长变化\n6. 家庭配合：家长积极配合治疗和护理"
            }
        ]
        
        self.dataset.extend(basic_qa)
    
    def generate_data_based_qa(self):
        """基于实际数据生成问答对"""
        print("正在基于实际数据生成问答对...")
        
        # 处理体重相关的表
        for table_name, df in self.data_tables.items():
            if '体重' in table_name and 'A.' in table_name and '年龄别' in table_name:
                gender = '男' if '男童' in table_name else '女'
                self._generate_weight_assessment_qa(df, gender, table_name)
            elif '身长' in table_name or '身高' in table_name and 'A.' in table_name:
                gender = '男' if '男童' in table_name else '女'
                self._generate_height_assessment_qa(df, gender, table_name)
            elif 'BMI' in table_name and 'A.' in table_name:
                gender = '男' if '男童' in table_name else '女'
                self._generate_bmi_assessment_qa(df, gender, table_name)
            elif '头围' in table_name and 'A.' in table_name:
                gender = '男' if '男童' in table_name else '女'
                self._generate_head_circumference_qa(df, gender, table_name)
    
    def _generate_weight_assessment_qa(self, df: pd.DataFrame, gender: str, table_name: str):
        """生成体重评估相关的问答对"""
        if '年龄' not in df.columns:
            return
            
        # 清理数据，只选择有效的行
        valid_rows = []
        for idx, row in df.iterrows():
            percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
            valid = True
            for col in percentile_cols:
                if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                    valid = False
                    break
            if valid and pd.notna(row['年龄']):
                valid_rows.append(row)
        
        if len(valid_rows) == 0:
            return
            
        # 随机选择几个年龄点生成问答
        sample_size = min(8, len(valid_rows))
        sample_indices = random.sample(range(len(valid_rows)), sample_size)
        
        for idx in sample_indices:
            row = valid_rows[idx]
            age = row['年龄']
            age_num, age_unit = self.parse_age_column(age)
            
            if age_num is None:
                continue
                
            # 生成评估问答，清理数值
            percentiles = {}
            for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                percentiles[col] = self._clean_numeric_value(row[col])
            
            # 生成不同体重值的评估
            test_weights = [
                percentiles['P10'] - 0.3,  # 偏低
                percentiles['P25'],        # 中下
                percentiles['P50'],        # 正常
                percentiles['P75'],        # 中上
                percentiles['P90'] + 0.5   # 偏高
            ]
            
            for weight in test_weights:
                if weight is None or weight <= 0:
                    continue
                    
                assessment = self.get_growth_assessment(weight, percentiles)
                advice = self.assessment_levels[assessment]['advice']
                
                qa = {
                    "instruction": f"请评估一个{age}的{gender}童体重{weight:.1f}kg的生长水平",
                    "input": "",
                    "output": f"根据7岁以下儿童生长发育标准，{age}的{gender}童体重{weight:.1f}kg属于{assessment}水平。参考标准：P50为{percentiles['P50']:.1f}kg，P25-P75范围为{percentiles['P25']:.1f}-{percentiles['P75']:.1f}kg。{advice}"
                }
                self.dataset.append(qa)
    
    def _generate_height_assessment_qa(self, df: pd.DataFrame, gender: str, table_name: str):
        """生成身高评估相关的问答对"""
        if '年龄' not in df.columns:
            return
        
        # 类似体重评估的逻辑，但针对身高
        valid_rows = []
        for idx, row in df.iterrows():
            percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
            valid = True
            for col in percentile_cols:
                if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                    valid = False
                    break
            if valid and pd.notna(row['年龄']):
                valid_rows.append(row)
        
        if len(valid_rows) == 0:
            return
            
        sample_size = min(5, len(valid_rows))
        sample_indices = random.sample(range(len(valid_rows)), sample_size)
        
        for idx in sample_indices:
            row = valid_rows[idx]
            age = row['年龄']
            
            percentiles = {}
            for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                percentiles[col] = self._clean_numeric_value(row[col])
            
            test_heights = [
                percentiles['P25'],        # 中下
                percentiles['P50'],        # 正常
                percentiles['P75']         # 中上
            ]
            
            for height in test_heights:
                if height is None or height <= 0:
                    continue
                    
                assessment = self.get_growth_assessment(height, percentiles)
                
                qa = {
                    "instruction": f"请评估一个{age}的{gender}童身高{height:.1f}cm的生长水平",
                    "input": "",
                    "output": f"根据7岁以下儿童生长发育标准，{age}的{gender}童身高{height:.1f}cm属于{assessment}水平。参考标准：P50为{percentiles['P50']:.1f}cm，正常范围P25-P75为{percentiles['P25']:.1f}-{percentiles['P75']:.1f}cm。"
                }
                self.dataset.append(qa)

    def _generate_bmi_assessment_qa(self, df: pd.DataFrame, gender: str, table_name: str):
        """生成BMI评估相关的问答对"""
        if '年龄' not in df.columns:
            return

        valid_rows = []
        for idx, row in df.iterrows():
            percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
            valid = True
            for col in percentile_cols:
                if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                    valid = False
                    break
            if valid and pd.notna(row['年龄']):
                valid_rows.append(row)

        if len(valid_rows) == 0:
            return

        sample_size = min(3, len(valid_rows))
        sample_indices = random.sample(range(len(valid_rows)), sample_size)

        for idx in sample_indices:
            row = valid_rows[idx]
            age = row['年龄']

            percentiles = {}
            for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                percentiles[col] = self._clean_numeric_value(row[col])

            test_bmis = [
                percentiles['P50'],        # 正常
                percentiles['P85'] if 'P85' in percentiles else percentiles['P90']  # 偏高
            ]

            for bmi in test_bmis:
                if bmi is None or bmi <= 0:
                    continue

                assessment = self.get_growth_assessment(bmi, percentiles)

                qa = {
                    "instruction": f"请评估一个{age}的{gender}童BMI{bmi:.1f}的营养状况",
                    "input": "",
                    "output": f"根据7岁以下儿童生长发育标准，{age}的{gender}童BMI{bmi:.1f}属于{assessment}水平。参考标准：P50为{percentiles['P50']:.1f}，正常范围P25-P75为{percentiles['P25']:.1f}-{percentiles['P75']:.1f}。"
                }
                self.dataset.append(qa)

    def _generate_head_circumference_qa(self, df: pd.DataFrame, gender: str, table_name: str):
        """生成头围评估相关的问答对"""
        if '年龄' not in df.columns:
            return

        valid_rows = []
        for idx, row in df.iterrows():
            percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
            valid = True
            for col in percentile_cols:
                if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                    valid = False
                    break
            if valid and pd.notna(row['年龄']):
                valid_rows.append(row)

        if len(valid_rows) == 0:
            return

        sample_size = min(3, len(valid_rows))
        sample_indices = random.sample(range(len(valid_rows)), sample_size)

        for idx in sample_indices:
            row = valid_rows[idx]
            age = row['年龄']

            percentiles = {}
            for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                percentiles[col] = self._clean_numeric_value(row[col])

            test_hcs = [percentiles['P50']]  # 正常值

            for hc in test_hcs:
                if hc is None or hc <= 0:
                    continue

                assessment = self.get_growth_assessment(hc, percentiles)

                qa = {
                    "instruction": f"请评估一个{age}的{gender}童头围{hc:.1f}cm的发育状况",
                    "input": "",
                    "output": f"根据7岁以下儿童生长发育标准，{age}的{gender}童头围{hc:.1f}cm属于{assessment}水平。参考标准：P50为{percentiles['P50']:.1f}cm，正常范围P25-P75为{percentiles['P25']:.1f}-{percentiles['P75']:.1f}cm。头围反映脑部发育状况。"
                }
                self.dataset.append(qa)

    def generate_comprehensive_scenarios(self):
        """生成综合评估场景"""
        scenarios = [
            {
                "instruction": "一个6月龄男婴，体重6.5kg，身长65cm，请进行生长评估",
                "input": "",
                "output": "根据7岁以下儿童生长发育标准：\n体重6.5kg：处于P10-P25之间，属于中下水平\n身长65cm：处于P25-P50之间，属于中等水平\n\n评估结论：该婴儿身长发育正常，但体重偏轻，建议：1）增加喂养频次；2）确保母乳或配方奶充足；3）定期监测体重增长；4）必要时咨询儿科医生。"
            },
            {
                "instruction": "一个2岁女童，体重15kg，身高85cm，BMI 20.8，如何评估？",
                "input": "",
                "output": "根据7岁以下儿童生长发育标准：\n体重15kg：超过P97，属于上水平\n身高85cm：处于P25-P50之间，属于中等水平\nBMI 20.8：超过P97，属于上水平\n\n评估结论：该女童存在超重问题，建议：1）调整饮食结构，减少高热量食物；2）增加体力活动；3）定期监测体重和BMI；4）咨询营养师制定合理饮食计划。"
            },
            {
                "instruction": "一个1岁男童，体重8kg，身长72cm，头围44cm，请评估发育状况",
                "input": "",
                "output": "根据7岁以下儿童生长发育标准：\n体重8kg：处于P3-P10之间，属于下水平\n身长72cm：处于P3-P10之间，属于下水平\n头围44cm：处于P10-P25之间，属于中下水平\n\n评估结论：该男童体重和身长均偏低，可能存在生长迟缓，建议：1）及时就医查找原因；2）评估营养摄入；3）排除疾病因素；4）制定个性化干预方案。"
            }
        ]

        self.dataset.extend(scenarios)

    def save_dataset(self, output_file: str = "comprehensive_growth_dataset.jsonl"):
        """保存综合数据集"""
        print(f"正在保存综合数据集到 {output_file}...")

        with open(output_file, 'w', encoding='utf-8') as f:
            for item in self.dataset:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')

        print(f"数据集已保存，共 {len(self.dataset)} 条记录")

        # 同时保存为JSON格式
        json_file = output_file.replace('.jsonl', '.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset, f, ensure_ascii=False, indent=2)

        print(f"同时保存JSON格式到 {json_file}")

    def generate_statistics(self):
        """生成数据集统计信息"""
        print("\n=== 综合数据集统计信息 ===")
        print(f"总样本数: {len(self.dataset)}")

        # 按类型统计
        instruction_types = {}
        for item in self.dataset:
            instruction = item['instruction']
            if '评估' in instruction:
                if '体重' in instruction:
                    key = '体重评估类'
                elif '身高' in instruction or '身长' in instruction:
                    key = '身高评估类'
                elif 'BMI' in instruction:
                    key = 'BMI评估类'
                elif '头围' in instruction:
                    key = '头围评估类'
                else:
                    key = '综合评估类'
            elif '如何' in instruction or '怎么' in instruction:
                key = '指导建议类'
            elif '什么是' in instruction or '为什么' in instruction:
                key = '基础知识类'
            else:
                key = '其他'

            instruction_types[key] = instruction_types.get(key, 0) + 1

        for type_name, count in instruction_types.items():
            print(f"{type_name}: {count} 条")

    def build_comprehensive_dataset(self):
        """构建综合数据集"""
        print("开始构建综合版婴幼儿体格生长监测数据集...")

        # 加载数据
        self.load_excel_data()

        # 生成各类样本
        self.generate_basic_knowledge_qa()
        self.generate_data_based_qa()
        self.generate_comprehensive_scenarios()

        # 打乱数据集
        random.shuffle(self.dataset)

        # 生成统计信息
        self.generate_statistics()

        # 保存数据集
        self.save_dataset()

        print("综合数据集构建完成！")

def main():
    """主函数"""
    excel_file = "7岁以下儿童生长发育标准（卫健委.xlsx"

    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return

    builder = ComprehensiveGrowthDatasetBuilder(excel_file)
    builder.build_comprehensive_dataset()

if __name__ == "__main__":
    main()
