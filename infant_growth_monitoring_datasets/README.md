# 婴幼儿体格生长监测数据集 (Infant Growth Monitoring Dataset)

## 数据集概述 (Dataset Overview)

本数据集专门用于训练和微调大语言模型，使其具备婴幼儿体格生长监测、评估和健康指导的专业能力。数据集基于中国卫生健康委员会发布的《7岁以下儿童生长发育标准》以及WHO儿童生长标准构建，涵盖体重、身高、BMI和头围等关键生长指标。

This dataset is specifically designed for training and fine-tuning large language models to provide professional infant and child growth monitoring, assessment, and health guidance capabilities. Built on the "Growth and Development Standards for Children Under 7 Years Old" published by China's National Health Commission and WHO child growth standards.

## 数据集特色 (Key Features)

- 🧠 **头围数据丰富**: 超过25%的样本包含头围信息，强调脑发育监测
- 📊 **多指标综合评估**: 同时评估体重、身高、BMI和头围
- 👶 **年龄覆盖全面**: 涵盖0-7岁儿童各年龄段
- 🏥 **专业医学标准**: 基于权威医学标准和临床实践
- 💬 **多样化场景**: 包含家长咨询、医生评估、体检报告等场景
- 🎯 **实用性强**: 直接适用于儿童保健和临床应用

## 数据集统计 (Dataset Statistics)

### 主要数据集文件

| 文件名 | 样本数 | 描述 |
|--------|--------|------|
| `growth_monitoring_400_samples.json/jsonl` | 400 | **主要数据集** - 最新最全面的版本 |
| `llm_assisted_growth_dataset.json/jsonl` | 200 | LLM辅助生成的高质量样本 |
| `comprehensive_growth_dataset.json/jsonl` | 140 | 综合评估样本 |
| `enhanced_growth_dataset.json/jsonl` | 140 | 增强版基础样本 |

### 400样本主数据集分布

| 指令类型 | 样本数 | 占比 | 说明 |
|----------|--------|------|------|
| 头围专项 | 96 | 24.0% | 专门的头围评估和脑发育指导 |
| 综合评估 | 90 | 22.5% | 体重+身高+BMI综合分析 |
| 体重评估 | 73 | 18.2% | 营养状态和体重发育评估 |
| 身高评估 | 64 | 16.0% | 线性生长和骨骼发育评估 |
| BMI评估 | 64 | 16.0% | 体重指数和营养状态评估 |
| 多指标+头围 | 12 | 3.0% | 包含头围的多指标综合评估 |
| 基础知识 | 1 | 0.3% | 生长发育相关知识问答 |

**包含头围信息的样本**: 108条 (27.0%)

## 数据格式 (Data Format)

数据集采用标准的指令微调格式：

```json
{
  "instruction": "请评估一个6月龄男婴体重7.2kg、身高65cm、头围42cm的综合发育情况",
  "input": "",
  "output": "1. 体重评估：该男婴体重7.2kg，处于6月龄男婴正常范围...\n2. 身高评估：身高65cm，符合同龄标准...\n3. 头围评估：头围42cm，反映脑发育良好..."
}
```

### 字段说明

- **instruction**: 用户的问题或指令，包含儿童的年龄、性别和生长指标数据
- **input**: 通常为空字符串
- **output**: 专业的医学评估和指导建议，包含详细的分析和建议

## 文件结构 (File Structure)

```
infant_growth_monitoring_datasets/
├── README.md                              # 本文件
├── dataset_info.json                      # 数据集元信息
│
├── 主要数据集 (Main Datasets)
├── growth_monitoring_400_samples.json     # 400样本主数据集 (JSON格式)
├── growth_monitoring_400_samples.jsonl    # 400样本主数据集 (JSONL格式)
├── llm_assisted_growth_dataset.json       # LLM辅助生成数据集
├── llm_assisted_growth_dataset.jsonl
├── comprehensive_growth_dataset.json      # 综合评估数据集
├── comprehensive_growth_dataset.jsonl
├── enhanced_growth_dataset.json           # 增强版数据集
├── enhanced_growth_dataset.jsonl
│
├── 训练分割数据集 (Training Splits)
├── train.jsonl/csv                        # 训练集
├── validation.jsonl/csv                   # 验证集
├── test.jsonl/csv                         # 测试集
│
├── 专项数据集 (Specialized)
├── head_circumference_sample_dataset.json # 头围样本数据集
├── head_circumference_instructions.json   # 头围指令数据集
└── validation_report.json                 # 数据验证报告
```

## 使用方法 (Usage)

### 1. 加载数据集

```python
import json

# 加载主数据集
with open('growth_monitoring_400_samples.json', 'r', encoding='utf-8') as f:
    dataset = json.load(f)

print(f"数据集包含 {len(dataset)} 个样本")
```

### 2. 使用训练分割

```python
# 加载预分割的训练数据
with open('train.jsonl', 'r', encoding='utf-8') as f:
    train_data = [json.loads(line) for line in f]

with open('validation.jsonl', 'r', encoding='utf-8') as f:
    val_data = [json.loads(line) for line in f]

with open('test.jsonl', 'r', encoding='utf-8') as f:
    test_data = [json.loads(line) for line in f]
```

### 3. 查看样本示例

```python
# 查看头围相关样本
head_samples = [item for item in dataset if '头围' in item['instruction']]
print(f"包含头围的样本数: {len(head_samples)}")
print("示例:", head_samples[0])
```

## 数据质量 (Data Quality)

### 质量保证措施

1. **权威数据源**: 基于卫健委和WHO官方标准
2. **专业医学审核**: 所有评估内容符合临床实践
3. **多样性验证**: 涵盖不同年龄、性别和生长状态
4. **一致性检查**: 确保评估逻辑和建议的一致性
5. **LLM辅助优化**: 使用先进AI技术提升内容质量

### 验证报告

详细的数据验证报告请参考 `validation_report.json`

## 应用场景 (Use Cases)

1. **儿童保健系统**: 自动化生长发育评估
2. **智能问诊助手**: 为家长提供专业指导
3. **医疗培训**: 医学生和儿科医生培训
4. **健康管理App**: 儿童成长监测功能
5. **科研分析**: 儿童生长发育研究

## 技术规格 (Technical Specifications)

- **数据格式**: JSON, JSONL, CSV
- **编码**: UTF-8
- **语言**: 中文 (简体)
- **领域**: 儿科医学、生长发育
- **标准**: 中国卫健委标准 + WHO标准



